'use strict';

const gulp = require('gulp');
const livereload = require('gulp-livereload');
const nodemon = require('gulp-nodemon');
const MailDev = require('maildev');
const config = require('./backend/src/services/config');

gulp.task('maildev', maildev);

gulp.task('watch', function () {
    livereload.listen();
    maildev();
    nodemon({stdout: false})
        .on('stdout', function (message) {
            console.log(message.toString().trim());
            if (message.includes('listening on port')) {
                livereload.reload();
            }
        })
        .on('stderr', function (message) {
            console.log(message.toString().trim());
        });
});


gulp.task('default',
    gulp.parallel(
        'watch'
    )
);

function maildev(callback) {
    if (config.app.env === 'production') {
        console.log('Production environment - skipping MailDev');
        return callback && callback();
    }

    // Check if we should use Docker MailDev instead of built-in MailDev
    if (config.maildev && config.maildev.useDocker === true) {
        console.log('✓ Using Docker MailDev container (built-in MailDev disabled)');
        console.log('  Web Interface: http://localhost:1080');
        console.log('  SMTP Server: localhost:1025');
        console.log('  To manage: pnpm maildev:docker:start|stop|restart');

        // Call callback immediately to continue gulp task
        if (callback) {
            callback();
        }
        return;
    }

    // Use built-in MailDev (legacy mode)
    console.log('Starting built-in MailDev...');
    let maildevInstance = new MailDev({
        basePathname: config.maildev.basePathname,
        ip: config.maildev.host,
        web: config.maildev.web,
        smtp: config.maildev.smtp,
        outgoingHost: config.sparkpost.host,
        outgoingPort: config.sparkpost.port,
        outgoingUser: config.sparkpost.username,
        outgoingPass: config.sparkpost.password,
        autoRelay: config.sparkpost.autoRelay
    });

    maildevInstance.listen(() => {
        console.log('Built-in MailDev started');
        if (callback) callback();
    });
}
