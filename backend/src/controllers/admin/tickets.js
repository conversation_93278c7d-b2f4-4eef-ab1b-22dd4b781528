'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require('bluebird');
const constants = require('../../models/constants');
const {HttpError} = require('../../services/error');
const regexEscape = require('escape-string-regexp');
const mw = require('../../services/middleware');
const Router = require('koa-router');

module.exports = Router()
    .prefix('/tickets')
    .param('ticketKey', paramTicket)
    .get('/', getTicketList)
    .get('/:ticketKey', getTicket)
    .post('/:ticketKey/messages', postTicketMessage)
    .patch('/:ticketKey', patchTicket);


async function paramTicket(ticketKey, ctx, next) {
    const {Ticket} = mongoose.models;
    const ticket = await Ticket.findOne({key: ticketKey}).populate('_vendor _retailer _product _productOrder');

    if (!ticket) {
        throw new HttpError(404, 'Ticket not found');
    }
    ctx.state.ticket = ticket;

    await next();
}

async function getTicketList(ctx) {
    const {Ticket} = mongoose.models;
    const payload = ctx.request.query;
    const query = {};
    const sort = payload.sort || '-createdAt';

    if (payload.status) {
        query.status = payload.status;
    }
    if (payload['q-order'] || payload['q-product'] || payload['q-vendor'] || payload['q-retailer']) {
        query.$and = [];
    }
    if (payload['q-order']) {
        const regex = new RegExp(regexEscape(payload['q-order']), 'i');
        query.$and.push({$or: [
            {'retailerOrder.key': {$regex: regex}},
            {'vendorOrder.key': {$regex: regex}},
            {'productOrder.key': {$regex: regex}}
        ]});
    }
    if (payload['q-product']) {
        const regex = new RegExp(regexEscape(payload['q-product']), 'i');
        query.$and.push({$or: [
            {'product.key': {$regex: regex}},
            {'product.name': {$regex: regex}}
        ]});
    }

    if (payload['q-vendor']) {
        const regex = new RegExp(regexEscape(payload['q-vendor']), 'i');
        query.$and.push({$or: [
            {'vendorUser.email': {$regex: regex}},
            {'vendor.storeName': {$regex: regex}},
            {'vendor.slug': {$regex: regex}},
            {'vendor.key': {$regex: regex}}
        ]});
    }

    if (payload['q-retailer']) {
        const regex = new RegExp(regexEscape(payload['q-retailer']), 'i');
        query.$and.push({$or: [
            {'retailerUser.email': {$regex: regex}},
            {'retailer.businessName': {$regex: regex}},
            {'retailer.key': {$regex: regex}}
        ]});
    }

    if (ctx.state.currentState) {
        query._state = ctx.state.currentState._id;
    }

    const {list, count, total} = await Bluebird.props({
        list: adminTicketList(query, sort)
            .skip((payload.page -1) * payload.perPage)
            .limit(payload.perPage)
            .exec(),

        count: adminTicketList(query, sort).count('count'),
        total: Ticket.count({})
    });
    ctx.body = {
        data: list,
        pagination: {
            count: count[0] && count[0].count || 0,
            total,
            page: payload.page,
            perPage: payload.perPage
        },
        filters: {...payload}
    };
}
async function getTicket(ctx) {

    ctx.body = {
        data: ctx.state.ticket.formatAdmin()
    };
}
async function patchTicket(ctx) {
    const {SystemLog} = mongoose.models;
    const {status, adminNotes} = ctx.request.body;
    const {ticket} = ctx.state;

    if (adminNotes) {
        const snapshot = SystemLog.takeSnapshot(ticket);
        ticket.set({adminNotes, processedAt: new Date()});
        await ticket.save();
        SystemLog.adminTickerUpdated(ctx, snapshot, ticket);
    }
    if (status !== ticket.status) {
        if (status === constants.TICKET.STATUS.CLOSED) {
            ticket.set({
                status: constants.TICKET.STATUS.CLOSED,
                closedBy: constants.TICKET.OPEN_BY.VENDOR,
                closedAt: new Date()
            });
            await ticket.save();
            SystemLog.adminTicketClosed(ctx, ticket);
        }
        else if (status === constants.TICKET.STATUS.OPENED) {
            ticket.set({
                status: constants.TICKET.STATUS.OPENED
            });
            await ticket.save();
            SystemLog.adminTicketReopened(ctx, ticket);
        }
    }
    ctx.body = {
        data: ticket.formatAdmin()
    };
}


async function postTicketMessage(ctx) {
    const {SystemLog} = mongoose.models;
    const {ticket} = ctx.state;
    const {text, images} = ctx.request.body;

    await ticket.addMessage(constants.TICKET.OPEN_BY.ADMIN, text, images);
    SystemLog.adminTicketMessageCreated(ctx, ticket, text, images);
    await ticket.populate('_vendor _product').execPopulate();
    ctx.body = {
        data: ticket.formatSource()
    };
}


function adminTicketList(query, sort) {
    const {Ticket} = mongoose.models;
    sort = {
        [sort.split('-').reverse()[0]]: sort.startsWith('-') ? -1 : 1
    };

    return Ticket.aggregate([
        {
            $lookup:
                {
                    from: 'vendors',
                    localField: '_vendor',
                    foreignField: '_id',
                    as: 'vendor'
                }
        },
        {
            $lookup:
                {
                    from: 'retailers',
                    localField: '_retailer',
                    foreignField: '_id',
                    as: 'retailer'
                }
        },
        {
            $lookup:
                {
                    from: 'products',
                    localField: '_product',
                    foreignField: '_id',
                    as: 'product'
                }
        },
        {
            $lookup:
                {
                    from: 'retailerOrders',
                    localField: '_retailerOrder',
                    foreignField: '_id',
                    as: 'retailerOrder'
                }
        },
        {
            $lookup:
                {
                    from: 'vendorOrders',
                    localField: '_vendorOrder',
                    foreignField: '_id',
                    as: 'vendorOrder'
                }
        },
        {
            $lookup:
                {
                    from: 'productOrders',
                    localField: '_productOrder',
                    foreignField: '_id',
                    as: 'productOrder'
                }
        },
        {
            $addFields: {
                vendor: {$arrayElemAt: ['$vendor', 0]},
                retailer: {$arrayElemAt: ['$retailer', 0]},
                product: {$arrayElemAt: ['$product', 0]},
                retailerOrder: {$arrayElemAt: ['$retailerOrder', 0]},
                vendorOrder: {$arrayElemAt: ['$vendorOrder', 0]},
                productOrder: {$arrayElemAt: ['$productOrder', 0]}
            }
        },
        {
            $match: query
        },
        {
            $sort: sort
        },
        {
            $project: {
                _id: 0,
                key: '$key',

                vendor: {
                    key: 1,
                    storeName: 1,
                    slug: 1
                },
                retailer: {
                    key: 1,
                    businessName: 1
                },
                product: {
                    key: 1,
                    name: 1
                },
                productOrder: {
                    key: 1,
                    name: 1
                },

                status: 1,
                subject: 1,
                category: 1,
                state: '$_state',
                createdAt: 1,
                updatedAt: 1
            }
        }
    ]);
}
