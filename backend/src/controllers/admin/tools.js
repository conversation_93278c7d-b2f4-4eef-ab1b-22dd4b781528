'use strict';

const Router = require('koa-router');
const mongoose = require('mongoose');
const exporter = require('../../services/exporter/index');

module.exports = Router()
    .prefix('/tools')
    .post('/export', postExport)
    .post('/export/special', postExportSpecial)
    .get('/applications/duplicates', getDuplicateApplications)
    .post('/applications/cleanup', postCleanupApplications);


async function postExport(ctx) {
    const {format, collection, filters} = ctx.request.body;

    if (!['csv', 'xlsx'].includes(format)) {
        ctx.throw(422, 'Wrong format');
    }

    const {stream, mime} = await exporter.exportTo(format, collection, filters);
    ctx.set('Content-Type', mime);
    ctx.body = stream;
}

async function postExportSpecial(ctx) {
    const {format, collection, filters} = ctx.request.body;

    if (!['csv', 'xlsx'].includes(format)) {
        ctx.throw(422, 'Wrong format');
    }

    const {stream, mime} = await exporter.specialExportTo(format, collection, filters);
    ctx.set('Content-Type', mime);
    ctx.body = stream;
}

async function getDuplicateApplications(ctx) {
    const { Vendor, Retailer, Application } = mongoose.models;

    try {
        const duplicateInfo = {
            vendors: [],
            retailers: [],
            summary: {
                vendorsWithDuplicates: 0,
                retailersWithDuplicates: 0,
                totalDuplicateApplications: 0
            }
        };

        // Find vendors with multiple applications
        const vendors = await Vendor.find({ deletedAt: { $exists: false } }).populate('_application');

        for (const vendor of vendors) {
            const allApplications = await Application.find({
                _vendor: vendor._id
            }).sort({ createdAt: 1 });

            if (allApplications.length > 1) {
                const correctApp = determineCorrectApplication(allApplications);
                const duplicateApps = allApplications.filter(app => app._id.toString() !== correctApp._id.toString());

                duplicateInfo.vendors.push({
                    key: vendor.key,
                    storeName: vendor.storeName,
                    currentApplication: vendor._application ? vendor._application.key : null,
                    currentApplicationStatus: vendor._application ? vendor._application.status : null,
                    totalApplications: allApplications.length,
                    applications: allApplications.map(app => ({
                        key: app.key,
                        status: app.status,
                        createdAt: app.createdAt,
                        isCurrent: vendor._application && app._id.toString() === vendor._application._id.toString(),
                        isRecommended: app._id.toString() === correctApp._id.toString()
                    })),
                    recommendedApplication: correctApp.key,
                    needsUpdate: !vendor._application || vendor._application._id.toString() !== correctApp._id.toString(),
                    duplicatesToDelete: duplicateApps.map(app => app.key)
                });

                duplicateInfo.summary.vendorsWithDuplicates++;
                duplicateInfo.summary.totalDuplicateApplications += duplicateApps.length;
            }
        }

        // Find retailers with multiple applications
        const retailers = await Retailer.find({ deletedAt: { $exists: false } }).populate('_application');

        for (const retailer of retailers) {
            const allApplications = await Application.find({
                _retailer: retailer._id
            }).sort({ createdAt: 1 });

            if (allApplications.length > 1) {
                const correctApp = determineCorrectApplication(allApplications);
                const duplicateApps = allApplications.filter(app => app._id.toString() !== correctApp._id.toString());

                duplicateInfo.retailers.push({
                    key: retailer.key,
                    storeName: retailer.storeName,
                    currentApplication: retailer._application ? retailer._application.key : null,
                    currentApplicationStatus: retailer._application ? retailer._application.status : null,
                    totalApplications: allApplications.length,
                    applications: allApplications.map(app => ({
                        key: app.key,
                        status: app.status,
                        createdAt: app.createdAt,
                        isCurrent: retailer._application && app._id.toString() === retailer._application._id.toString(),
                        isRecommended: app._id.toString() === correctApp._id.toString()
                    })),
                    recommendedApplication: correctApp.key,
                    needsUpdate: !retailer._application || retailer._application._id.toString() !== correctApp._id.toString(),
                    duplicatesToDelete: duplicateApps.map(app => app.key)
                });

                duplicateInfo.summary.retailersWithDuplicates++;
                duplicateInfo.summary.totalDuplicateApplications += duplicateApps.length;
            }
        }

        ctx.body = {
            data: duplicateInfo
        };

    } catch (error) {
        console.error('Error getting duplicate applications:', error);
        ctx.throw(500, 'Failed to analyze duplicate applications');
    }
}

async function postCleanupApplications(ctx) {
    const { Vendor, Retailer, Application } = mongoose.models;
    const { dryRun = true } = ctx.request.body;

    // Validate input
    if (typeof dryRun !== 'boolean') {
        ctx.throw(422, 'dryRun must be a boolean');
    }

    try {
        const results = {
            dryRun,
            vendorsProcessed: 0,
            retailersProcessed: 0,
            vendorsFixed: 0,
            retailersFixed: 0,
            applicationsDeleted: 0,
            actions: [],
            errors: []
        };

        // Process vendors
        const vendors = await Vendor.find({ deletedAt: { $exists: false } });

        for (const vendor of vendors) {
            results.vendorsProcessed++;

            try {
                const allApplications = await Application.find({
                    _vendor: vendor._id
                }).sort({ createdAt: 1 });

                if (allApplications.length <= 1) {
                    // Check if single application reference is correct
                    if (allApplications.length === 1) {
                        const app = allApplications[0];
                        const needsUpdate = !vendor._application || vendor._application.toString() !== app._id.toString();

                        if (needsUpdate) {
                            results.actions.push({
                                type: 'update_reference',
                                entity: 'vendor',
                                key: vendor.key,
                                storeName: vendor.storeName,
                                action: `Update _application reference to ${app.key}`
                            });

                            if (!dryRun) {
                                vendor._application = app._id;
                                await vendor.save();
                            }
                            results.vendorsFixed++;
                        }
                    }
                    continue;
                }

                // Handle duplicates
                const correctApp = determineCorrectApplication(allApplications);
                const duplicateApps = allApplications.filter(app => app._id.toString() !== correctApp._id.toString());

                results.actions.push({
                    type: 'cleanup_duplicates',
                    entity: 'vendor',
                    key: vendor.key,
                    storeName: vendor.storeName,
                    keepApplication: correctApp.key,
                    deleteApplications: duplicateApps.map(app => app.key),
                    totalApplications: allApplications.length
                });

                // Update vendor reference
                const needsUpdate = !vendor._application || vendor._application.toString() !== correctApp._id.toString();
                if (needsUpdate && !dryRun) {
                    vendor._application = correctApp._id;
                    await vendor.save();
                }

                // Delete duplicates
                if (!dryRun) {
                    for (const duplicateApp of duplicateApps) {
                        await Application.deleteOne({ _id: duplicateApp._id });
                    }
                }

                results.vendorsFixed++;
                results.applicationsDeleted += duplicateApps.length;

            } catch (error) {
                results.errors.push({
                    entity: 'vendor',
                    key: vendor.key,
                    error: error.message
                });
            }
        }

        // Process retailers (similar logic)
        const retailers = await Retailer.find({ deletedAt: { $exists: false } });

        for (const retailer of retailers) {
            results.retailersProcessed++;

            try {
                const allApplications = await Application.find({
                    _retailer: retailer._id
                }).sort({ createdAt: 1 });

                if (allApplications.length <= 1) {
                    if (allApplications.length === 1) {
                        const app = allApplications[0];
                        const needsUpdate = !retailer._application || retailer._application.toString() !== app._id.toString();

                        if (needsUpdate) {
                            results.actions.push({
                                type: 'update_reference',
                                entity: 'retailer',
                                key: retailer.key,
                                storeName: retailer.storeName,
                                action: `Update _application reference to ${app.key}`
                            });

                            if (!dryRun) {
                                retailer._application = app._id;
                                await retailer.save();
                            }
                            results.retailersFixed++;
                        }
                    }
                    continue;
                }

                const correctApp = determineCorrectApplication(allApplications);
                const duplicateApps = allApplications.filter(app => app._id.toString() !== correctApp._id.toString());

                results.actions.push({
                    type: 'cleanup_duplicates',
                    entity: 'retailer',
                    key: retailer.key,
                    storeName: retailer.storeName,
                    keepApplication: correctApp.key,
                    deleteApplications: duplicateApps.map(app => app.key),
                    totalApplications: allApplications.length
                });

                const needsUpdate = !retailer._application || retailer._application.toString() !== correctApp._id.toString();
                if (needsUpdate && !dryRun) {
                    retailer._application = correctApp._id;
                    await retailer.save();
                }

                if (!dryRun) {
                    for (const duplicateApp of duplicateApps) {
                        await Application.deleteOne({ _id: duplicateApp._id });
                    }
                }

                results.retailersFixed++;
                results.applicationsDeleted += duplicateApps.length;

            } catch (error) {
                results.errors.push({
                    entity: 'retailer',
                    key: retailer.key,
                    error: error.message
                });
            }
        }

        ctx.body = {
            data: results
        };

    } catch (error) {
        console.error('Error cleaning up applications:', error);
        ctx.throw(500, 'Failed to cleanup duplicate applications');
    }
}

/**
 * Determine which application should be kept based on priority:
 * 1. Accepted applications (prefer first accepted)
 * 2. Pending applications (prefer most recent)
 * 3. Original application (fallback)
 */
function determineCorrectApplication(applications) {
    // Priority 1: Accepted applications
    const acceptedApps = applications.filter(app => app.status === 'Accepted');
    if (acceptedApps.length > 0) {
        return acceptedApps[0]; // First accepted application
    }

    // Priority 2: Pending applications (most recent)
    const pendingApps = applications.filter(app => app.status === 'Pending');
    if (pendingApps.length > 0) {
        return pendingApps[pendingApps.length - 1]; // Most recent pending
    }

    // Priority 3: Most recent non-draft application
    const nonDraftApps = applications.filter(app => app.status !== 'Draft');
    if (nonDraftApps.length > 0) {
        return nonDraftApps[nonDraftApps.length - 1];
    }

    // Fallback: Original application (first created)
    return applications[0];
}
