'use strict';

const config = require('../config');
const mongoose = require('mongoose');
const constants = require('../../models/constants');
const queue = require('../queue');



async function retailerApplicationSubmittedEmail({application}) {
    const {EmailLog, User} = mongoose.models;

    if (!Array.isArray(config.applicationsEmails) || !config.applicationsEmails.length) {
        return;
    }
    await application.populate('_retailer').execPopulate();
    const user = await User.findById(application._retailer._user);

    const emailLogs = await Promise.all(config.applicationsEmails.map(adminEmail => {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.RETAILER_APPLICATION_SUBMITTED,
            toEmail: adminEmail,
            toName: 'Admin',
            subject: 'Action Required - New Retailer Application Submitted',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.RETAILER_APPLICATION_SUBMITTED,
            context: {
                adminEmail,
                retailerName: application._retailer.businessName,
                reviewApplicationUrl: `${config.app.url}/admin/applications/${String(application.key)}`
            },
            _application: application,
            _retailer: application._retailer,
            _user: user
        });
        return emailLog.save();
    }));
    emailLogs.forEach(emailLog => {
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    });
}

async function retailerApplicationUpdatedEmail({application}) {
    const {EmailLog, User} = mongoose.models;

    if (!Array.isArray(config.applicationsEmails) || !config.applicationsEmails.length) {
        return;
    }
    await application.populate('_retailer').execPopulate();
    const user = await User.findById(application._retailer._user);

    const emailLogs = await Promise.all(config.applicationsEmails.map(adminEmail => {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.RETAILER_APPLICATION_UPDATED,
            toEmail: adminEmail,
            toName: 'Admin',
            subject: 'Action Required - Retailer Application Updated',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.RETAILER_APPLICATION_UPDATED,
            context: {
                adminEmail,
                retailerName: application._retailer.storeName,
                reviewApplicationUrl: `${config.app.url}/admin/applications/${String(application.key)}`
            },
            _application: application,
            _retailer: application._retailer,
            _user: user
        });
        return emailLog.save();
    }));
    emailLogs.forEach(emailLog => {
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    });
}

async function retailerApplicationAcceptedEmail({application}) {
    const {EmailLog} = mongoose.models;

    await application.populate('_retailer').execPopulate();
    const userList = await application._retailer.getTeamActiveUsers();
    for (const user of userList) {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.RETAILER_APPLICATION_ACCEPTED,
            toEmail: user.email,
            toName: user.name,
            subject: 'Your CanIDeal Application Has Been Accepted',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.RETAILER_APPLICATION_ACCEPTED,
            context: {
                userName: user.name,
                manageStoreUrl: `${config.app.url}/retailer`
            },
            _application: application,
            _retailer: application._retailer,
            _user: user
        });
        await emailLog.save();
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    }
}

async function retailerApplicationDeclinedEmail({application}) {
    const {EmailLog} = mongoose.models;

    await application.populate('_retailer').execPopulate();
    const userList = await application._retailer.getTeamActiveUsers();
    for (const user of userList) {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.RETAILER_APPLICATION_DECLINED,
            toEmail: user.email,
            toName: user.name,
            subject: 'Your CanIDeal Account Can’t Be Activated',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.RETAILER_APPLICATION_DECLINED,
            context: {
                userName: user.name,
                resubmitApplicationUrl: `${config.app.url}/retailer/application`
            },
            _application: application,
            _retailer: application._retailer,
            _user: user
        });
        await emailLog.save();
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    }
}

module.exports = {
    retailerApplicationSubmittedEmail,
    retailerApplicationUpdatedEmail,
    retailerApplicationAcceptedEmail,
    retailerApplicationDeclinedEmail
};
