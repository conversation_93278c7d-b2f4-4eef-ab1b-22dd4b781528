'use strict';

const config = require('../config');
const constants = require('../../models/constants');
const SparkPost = require('sparkpost');
const nodemailer = require('nodemailer');
const view = require('../view');
const sp = config.sparkpost && config.sparkpost.apiKey && config.sparkpost.apiKey ? new SparkPost(config.sparkpost.apiKey) : null;
const spEmailBlast = config.sparkpost && config.sparkpost.apiKey && config.sparkpost.apiKey ? new SparkPost(config.sparkpost.apiKey) : null;
const url = require('url');
const { changeEmail, restorePasswordEmail, signupEmail, verifyEmail, teammateInviteEmail, reminderEmailForDraft, adminNewSignupEmail } = require('./user');
const { vendorApplicationSubmittedEmail, vendorApplicationUpdatedEmail, vendorApplicationAcceptedEmail, vendorApplicationDeclinedEmail } = require('./vendor');
const { retailerApplicationSubmittedEmail, retailerApplicationUpdatedEmail, retailerApplicationAcceptedEmail, retailerApplicationDeclinedEmail } = require('./retailer');
const { retailerOrderMissingDocument, vendorOrderMissingDocument, retailerOrderCreated, retailerOrderShipped, retailerOrderDelivered, retailerOrderRejected, vendorNewOrder, vendorOrderDelivered, vendorOrderFundsReleased, adminNewOrder } = require('./order');
const { sendRetailerMessage, sendVendorMessage } = require('./ticket');

const mailer = nodemailer.createTransport({
    host: config.maildev.host, // i.e. '127.0.0.1'
    port: config.maildev.smtp, // i.e. 3202 ,
    ignoreTLS: true
});
const excludeEmailDomains = /@example\.(com|org)/i;
const subjectWithEnvPrefix = subject => (config.app.env !== constants.ENV.PRODUCTION ? config.app.env.toUpperCase() + ': ' : '') + subject;

module.exports = {
    send,
    sendEmailBlast,
    create: {
        restorePasswordEmail,
        signupEmail,
        changeEmail,
        verifyEmail,
        teammateInviteEmail,

        reminderEmailForDraft,
        vendorApplicationSubmittedEmail,
        vendorApplicationUpdatedEmail,
        vendorApplicationAcceptedEmail,
        vendorApplicationDeclinedEmail,
        retailerApplicationSubmittedEmail,
        retailerApplicationUpdatedEmail,
        retailerApplicationAcceptedEmail,
        retailerApplicationDeclinedEmail,

        sendRetailerMessage,
        sendVendorMessage,

        retailerOrderMissingDocument,
        vendorOrderMissingDocument,
        retailerOrderCreated,
        retailerOrderShipped,
        retailerOrderDelivered,
        retailerOrderRejected,
        vendorNewOrder,
        vendorOrderDelivered,
        vendorOrderFundsReleased,
        adminNewOrder,
        adminNewSignupEmail
    }
};


function send(emailLog) {
    const { toEmail, toName, subject, fromName, fromEmail, template, context } = emailLog;
    const html = view.renderEmail(template + '.html', context);
    const txt = view.renderEmail(template + '.txt', context);

    if (config.app.env !== constants.ENV.PRODUCTION) {
        sendByNodemailer(toEmail, subject, toName, html, fromName, fromEmail);
    }

    if (!excludeEmailDomains.test(toEmail) && [constants.ENV.STAGING, constants.ENV.PRODUCTION].includes(config.app.env)) {
        return sendBySparkpost(emailLog, html, txt);
    }
    return Promise.resolve();
}

async function sendEmailBlast(emailBlast, data) {
    const emailTemplate = emailBlast._emailTemplate;


    if (config.app.env !== constants.ENV.PRODUCTION) {
        sendByNodemailer(data.email, data.subject, data.name, data.html, emailTemplate.fromName, emailTemplate.fromEmail);
    }

    if (!excludeEmailDomains.test(data.email) && [constants.ENV.STAGING, constants.ENV.PRODUCTION].includes(config.app.env)) {
        return sendEmailBlastBySparkpost(data, emailBlast._emailTemplate, emailBlast.key);
    }
}

function sendBySparkpost(emailLog, html, txt) {
    const { toEmail, toName, subject, fromEmail, fromName, replyTo, attachments } = emailLog;

    if (!config.sparkpost.apiKey) {
        throw new Error('No Sparkpost key provided');
    }

    /* eslint camelcase: off */
    let message = {
        return_path: config.sparkpost.returnPath,
        options: {
            open_tracking: true,
            click_tracking: true,
            transactional: true,
            debug: true
        },
        content: {
            from: {
                name: fromName || config.emails.senderName,
                email: fromEmail || config.emails.senderEmail
            },
            subject: subjectWithEnvPrefix(subject),
            html,
            text: txt
        },
        recipients: [
            {
                address: {
                    email: toEmail,
                    name: toName
                }
            }
        ],
        metadata: {
            env: config.app.env,
            messageId: String(emailLog._id)
        }
    };
    if (replyTo) {
        message.content.reply_to = replyTo;
    }
    if (attachments && attachments.length) {
        message.content.attachments = attachments.map(attachment => (
            {
                name: attachment.name,
                type: attachment.mimeType,
                data: attachment.content.toString('base64')
            }
        ));
    }
    // TODO: Debug - This was commented before because there is an error with an empty ip pool
    // if ([constants.ENV.PRODUCTION].includes(config.app.env)) {
    //     message.options.ip_pool = config.sparkpost.ipPool;
    // }
    if (!sp) {
        throw new Error('No Sparkpost key');
    }
    // TODO: This was the email issue that is now fixed
    return sp.transmissions.send(message);
}
function sendEmailBlastBySparkpost({ name: toName, email: toEmail, html, txt, key, subject }, { from, replyTo }, emailBlastKey) {

    if (!spEmailBlast) {
        throw new Error('No Sparkpost key provided');
    }

    /* eslint camelcase: off */
    let message = {
        return_path: config.sparkpost.blastReturnPath,
        options: {
            open_tracking: true,
            click_tracking: true
        },
        content: {
            from,
            subject: subjectWithEnvPrefix(subject),
            html,
            text: txt
        },
        recipients: [
            {
                address: {
                    email: toEmail,
                    name: toName
                }
            }
        ],
        metadata: {
            env: config.app.env,
            emailBlastKey,
            userKey: key
        }
    };
    if (replyTo) {
        message.content.reply_to = replyTo;
    }
    // if (attachments && attachments.length) {
    //     message.content.attachments = attachments.map(attachment => (
    //         {
    //             name: attachment.name,
    //             type: attachment.mimeType,
    //             data: attachment.content.toString('base64')
    //         }
    //     ));
    // }
    // if ([constants.ENV.PRODUCTION].includes(config.app.env)) {
    //     message.options.ip_pool = config.sparkpost.ipPool;
    // }

    return spEmailBlast.transmissions.send(message);
}


function sendByNodemailer(email, subject, name, html, fromName, fromEmail) {
    let options = {
        from: `"${fromName || config.emails.senderName}" <${fromEmail || config.emails.senderEmail}>`,
        to: `"${name}" <${email}>`,
        subject,
        html
    };

    mailer.sendMail(options, function (error) {
        if (error) {
            console.error(
                'Can\'t send email to maildev. Start it using "pnpm maildev:docker:start"\n'
                + `From: ${options.from}\n`
                + `To: ${options.to}\n`
                + `Subject: ${options.subject}\n`
                + error.toString()
            );
        }
    });
}
