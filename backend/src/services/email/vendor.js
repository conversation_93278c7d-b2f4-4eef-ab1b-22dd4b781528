'use strict';

const config = require('../config');
const mongoose = require('mongoose');
const constants = require('../../models/constants');
const queue = require('../queue');



async function vendorApplicationSubmittedEmail({application}) {
    const {EmailLog, User} = mongoose.models;

    if (!Array.isArray(config.applicationsEmails) || !config.applicationsEmails.length) {
        return;
    }
    await application.populate('_vendor').execPopulate();
    const user = await User.findById(application._vendor._user);

    const emailLogs = await Promise.all(config.applicationsEmails.map(adminEmail => {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.VENDOR_APPLICATION_SUBMITTED,
            toEmail: adminEmail,
            toName: 'Admin',
            subject: 'Action Required - New Vendor Application Submitted',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.VENDOR_APPLICATION_SUBMITTED,
            context: {
                adminEmail,
                vendorName: application._vendor.storeName,
                reviewApplicationUrl: `${config.app.url}/admin/applications/${String(application.key)}`
            },
            _application: application,
            _vendor: application._vendor,
            _user: user
        });
        return emailLog.save();
    }));
    emailLogs.forEach(emailLog => {
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    });
}

async function vendorApplicationUpdatedEmail({application}) {
    const {EmailLog, User} = mongoose.models;

    if (!Array.isArray(config.applicationsEmails) || !config.applicationsEmails.length) {
        return;
    }
    await application.populate('_vendor').execPopulate();
    const user = await User.findById(application._vendor._user);

    const emailLogs = await Promise.all(config.applicationsEmails.map(adminEmail => {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.VENDOR_APPLICATION_UPDATED,
            toEmail: adminEmail,
            toName: 'Admin',
            subject: 'Action Required - Vendor Application Updated',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.VENDOR_APPLICATION_UPDATED,
            context: {
                adminEmail,
                vendorName: application._vendor.storeName,
                reviewApplicationUrl: `${config.app.url}/admin/applications/${String(application.key)}`
            },
            _application: application,
            _vendor: application._vendor,
            _user: user
        });
        return emailLog.save();
    }));
    emailLogs.forEach(emailLog => {
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    });
}

async function vendorApplicationAcceptedEmail({application}) {
    const {EmailLog} = mongoose.models;

    await application.populate('_vendor').execPopulate();
    const userList = await application._vendor.getTeamActiveUsers();
    for (const user of userList) {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.VENDOR_APPLICATION_ACCEPTED,
            toEmail: user.email,
            toName: user.name,
            subject: 'Your CanIDeal Account Has Been Activated',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.VENDOR_APPLICATION_ACCEPTED,
            context: {
                userName: user.name,
                manageStoreUrl: `${config.app.url}/vendor`
            },
            _application: application,
            _vendor: application._vendor,
            _user: user
        });
        await emailLog.save();
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    }
}

async function vendorApplicationDeclinedEmail({application}) {
    const {EmailLog} = mongoose.models;

    await application.populate('_vendor').execPopulate();
    const userList = await application._vendor.getTeamActiveUsers();
    for (const user of userList) {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.VENDOR_APPLICATION_DECLINED,
            toEmail: user.email,
            toName: user.name,
            subject: 'Your CanIDeal Account Can’t Be Activated',
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.VENDOR_APPLICATION_DECLINED,
            context: {
                userName: user.name,
                resubmitApplicationUrl: `${config.app.url}/vendor/application`
            },
            _application: application,
            _vendor: application._vendor,
            _user: user
        });
        await emailLog.save();
        queue.emails.sendEmail({emailLogId: String(emailLog._id), type: emailLog.type});
    }
}

module.exports = {
    vendorApplicationSubmittedEmail,
    vendorApplicationUpdatedEmail,
    vendorApplicationAcceptedEmail,
    vendorApplicationDeclinedEmail
};
