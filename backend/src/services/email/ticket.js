'use strict';

const config = require('../config');
const mongoose = require('mongoose');
const constants = require('../../models/constants');
const { DateTime } = require('luxon');
const queue = require('../queue');

module.exports = {
    sendRetailerMessage,
    sendVendorMessage
};

async function sendRetailerMessage({ ticket }) {
    const { EmailLog } = mongoose.models;
    await ticket.populate('_retailer _vendor _vendorOrder').execPopulate();

    const userList = await ticket._vendor.getTeamActiveUsers();
    for (const user of userList) {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.RETAILER_TICKET_MESSAGE,
            toEmail: user.email,
            toName: user.name,
            subject: `New Message from ${ticket._retailer.businessName}`,
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.RETAILER_TICKET_MESSAGE,
            context: {
                ticketKey: ticket.key,
                retailer: ticket._retailer.formatPublic(),
                vendorOrder: ticket._vendorOrder.formatVendor(),
                user: user.formatPublic(),
                message: ticket.messages[ticket.messages.length - 1],
                url: config.app.url,
            },
            _retailerOrder: ticket._retailerOrder,
            _vendorOrder: ticket._vendorOrder._id,
            _productOrder: ticket._productOrder,
            _vendor: ticket._vendor._id,
            _retailer: ticket._retailer._id,
            _user: user
        });
        await emailLog.save();

        queue.emails.sendEmail({ emailLogId: String(emailLog._id), type: emailLog.type });
    }
}

async function sendVendorMessage({ ticket }) {
    const { EmailLog } = mongoose.models;
    await ticket.populate('_retailer _vendor _retailerOrder').execPopulate();

    // For general support tickets (no retailer), we might want to send to admin instead
    // For now, we'll skip email notifications for general tickets to avoid errors
    if (!ticket._retailer || !ticket._retailerOrder) {
        console.log('Skipping email notification for general support ticket:', ticket.key);
        return;
    }

    const userList = await ticket._vendor.getTeamActiveUsers();
    for (const user of userList) {
        const emailLog = new EmailLog({
            type: constants.EMAIL.TYPE.VENDOR_TICKET_MESSAGE,
            toEmail: user.email,
            toName: user.name,
            subject: `New Message from ${ticket._vendor.storeName}`,
            fromEmail: config.emails.senderEmail,
            fromName: config.emails.senderName,
            replyTo: config.emails.replyTo,
            template: constants.EMAIL.TEMPLATE.VENDOR_TICKET_MESSAGE,
            context: {
                ticketKey: ticket.key,
                vendor: ticket._vendor.formatPublic(),
                retailerOrder: ticket._retailerOrder.formatPublic(),
                user: user.formatPublic(),
                message: ticket.messages[ticket.messages.length - 1],
                url: config.app.url,
            },
            _retailerOrder: ticket._retailerOrder._id,
            _vendorOrder: ticket._vendorOrder,
            _productOrder: ticket._productOrder,
            _vendor: ticket._vendor._id,
            _retailer: ticket._retailer._id,
            _user: user
        });
        await emailLog.save();

        queue.emails.sendEmail({ emailLogId: String(emailLog._id), type: emailLog.type });
    }
}

