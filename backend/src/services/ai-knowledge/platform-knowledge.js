'use strict';

// CanIDeal Platform Knowledge Base for AI Assistant
const PLATFORM_KNOWLEDGE = {
    // General Platform Information
    platform: {
        name: 'CanIDeal',
        description: 'B2B cannabis marketplace connecting vendors with retailers',
        purpose: 'Facilitate wholesale cannabis transactions between licensed businesses',
        userTypes: ['vendors', 'retailers', 'admins']
    },

    // User Roles and Capabilities
    roles: {
        vendor: {
            description: 'Cannabis suppliers who list and sell products',
            capabilities: [
                'List products and manage inventory',
                'Receive and fulfill orders from retailers',
                'Manage business profile and settings',
                'Upload product images and descriptions',
                'Set pricing and availability',
                'Communicate with retailers through tickets',
                'View sales analytics and reports',
                'Manage Metrc integration (for domestic vendors)',
                'Import products via spreadsheet upload'
            ],
            commonTasks: [
                'Adding new products',
                'Updating inventory levels',
                'Processing orders',
                'Changing account settings',
                'Uploading product images',
                'Managing business information',
                'Enabling THC product sales',
                'Updating application for THC products'
            ],
            dashboardSections: [
                'Dashboard (overview)',
                'Products (inventory management)',
                'Orders (order fulfillment)',
                'Tickets (customer communication)',
                'Settings (account management)'
            ]
        },
        retailer: {
            description: 'Cannabis retailers who purchase products for resale',
            capabilities: [
                'Browse and search vendor products',
                'Place orders with vendors',
                'Manage business profile and settings',
                'Track order history and status',
                'Communicate with vendors through tickets',
                'Set purchasing preferences',
                'Manage payment methods',
                'View spending analytics'
            ],
            commonTasks: [
                'Searching for products',
                'Placing orders',
                'Tracking deliveries',
                'Managing account settings',
                'Contacting vendors',
                'Updating business information'
            ],
            dashboardSections: [
                'Dashboard (overview)',
                'Products (product catalog)',
                'Orders (purchase history)',
                'Tickets (vendor communication)',
                'Settings (account management)'
            ]
        },
        admin: {
            description: 'Platform administrators who manage the marketplace',
            capabilities: [
                'Manage user accounts and applications',
                'Review and approve vendor/retailer applications',
                'Monitor platform activity and analytics',
                'Manage email campaigns and communications',
                'Handle customer support and disputes',
                'Configure platform settings',
                'Export data and generate reports',
                'Manage regulatory compliance'
            ],
            commonTasks: [
                'Reviewing applications',
                'Managing user accounts',
                'Sending email campaigns',
                'Generating reports',
                'Handling support tickets',
                'Platform configuration'
            ],
            dashboardSections: [
                'Dashboard (overview)',
                'Users (account management)',
                'Applications (approval workflow)',
                'Orders (transaction monitoring)',
                'Email Blasts (marketing campaigns)',
                'Analytics (platform metrics)'
            ]
        }
    },

    // Common User Tasks and How-To Guides
    commonTasks: {
        changePassword: {
            steps: [
                'Go to Settings or Account section',
                'Look for "Change Password" or "Security" option',
                'Enter current password',
                'Enter new password twice to confirm',
                'Click "Update Password" or "Save"'
            ],
            location: 'Settings > Account > Security'
        },
        updateProfile: {
            steps: [
                'Navigate to Settings or Profile section',
                'Click "Edit Profile" or similar button',
                'Update the desired information',
                'Save changes by clicking "Update" or "Save"'
            ],
            location: 'Settings > Profile'
        },
        contactSupport: {
            steps: [
                'Look for "Support" or "Help" in the main navigation',
                'Click "Create Ticket" or "Contact Support"',
                'Fill out the support form with your issue',
                'Submit the ticket and wait for response'
            ],
            location: 'Support > Create Ticket'
        },
        addProduct: {
            role: 'vendor',
            steps: [
                'Go to Products section',
                'Click "Add Product" or "+" button',
                'Fill in product details (name, description, price)',
                'Upload product images',
                'Set inventory levels and availability',
                'Save the product'
            ],
            location: 'Products > Add Product'
        },
        placeOrder: {
            role: 'retailer',
            steps: [
                'Browse products or search for specific items',
                'Click on product to view details',
                'Select quantity and add to cart',
                'Review cart and proceed to checkout',
                'Confirm order details and submit'
            ],
            location: 'Products > Product Details > Add to Cart'
        },
        importProducts: {
            role: 'vendor',
            steps: [
                'Go to Products section',
                'Look for "Import" or "Upload" button',
                'Download the template spreadsheet',
                'Fill in product information in the template',
                'Upload the completed spreadsheet',
                'Review and confirm the import'
            ],
            location: 'Products > Import Products'
        },
        enableThcProducts: {
            role: 'vendor',
            steps: [
                'Go to your vendor application (if not yet submitted)',
                'Find the section about THC products or cannabis license',
                'Toggle "I am applying to sell THC products or Flower derived CBD products" to Yes',
                'Fill in required license information',
                'Submit or update your application',
                'Wait for admin approval to make THC products publicly visible'
            ],
            location: 'Application > License Information',
            note: 'You can create THC products before approval, but they will remain hidden until approved'
        },
        addThcProduct: {
            role: 'vendor',
            steps: [
                'Ensure THC products are enabled in your application',
                'Go to Products section',
                'Click "Add Product" or "+" button',
                'Select "THC" or "Flower derived CBD" as product type',
                'If you are a broker, fill in seller license information (license number, holder name, expiration date)',
                'Fill in all required product details',
                'Upload product images and COA documents',
                'Set pricing and inventory levels',
                'Save the product (will be hidden until application approval)'
            ],
            location: 'Products > Add Product',
            requirements: ['THC enabled in application', 'Valid cannabis license (or seller license for brokers)', 'COA documents for compliance']
        },
        brokerThcProducts: {
            role: 'vendor',
            steps: [
                'Ensure you are registered as a broker account',
                'Enable THC products in your vendor application',
                'When creating THC products, provide seller license information:',
                '  - Seller License Number (required)',
                '  - License Holder Name (required)',
                '  - License Expiration Date (required)',
                'Complete all other product details normally',
                'Products will be hidden until your application is approved'
            ],
            location: 'Products > Add Product',
            note: 'Brokers do not need their own cannabis license but must provide valid seller license information for each THC product'
        }
    },

    // Troubleshooting Common Issues
    troubleshooting: {
        loginIssues: [
            'Check if email and password are correct',
            'Try resetting password if forgotten',
            'Clear browser cache and cookies',
            'Try a different browser or incognito mode',
            'Contact support if issues persist'
        ],
        uploadIssues: [
            'Check file size (usually max 10MB per image)',
            'Ensure file format is supported (JPG, PNG, PDF)',
            'Try a different browser',
            'Check internet connection',
            'Contact support for persistent issues'
        ],
        orderIssues: [
            'Check if all required fields are filled',
            'Verify payment method is valid',
            'Ensure sufficient account balance/credit',
            'Contact vendor directly through tickets',
            'Reach out to support for assistance'
        ],
        thcProductIssues: [
            'If THC product types are not available in the dropdown, your application may not include THC product sales',
            'Update your vendor application to include "I am applying to sell THC products"',
            'THC products will not be publicly visible until your application is approved by an admin',
            'Ensure you have a valid cannabis license with expiration date',
            'For brokers: you can create THC products but must provide complete seller license information (number, holder name, expiration date)',
            'Broker accounts do not need their own cannabis license but must have valid seller license details for each THC product',
            'Contact support to update your application status or resolve licensing issues'
        ],
        applicationIssues: [
            'If you cannot submit your application, check that all required fields are completed',
            'Make sure to upload all required documents (ID, proof of address, etc.)',
            'For THC products, ensure license information is complete and valid',
            'Applications typically take 1-5 business days to review',
            'You can edit your application before it is approved',
            'Contact support if your application has been pending for more than 5 business days'
        ]
    },

    // Platform Features and Integrations
    features: {
        metrcIntegration: {
            description: 'Integration with Metrc for compliance tracking',
            applicableTo: 'domestic vendors',
            purpose: 'Track cannabis products for regulatory compliance',
            setup: 'Settings > Metrc Integration'
        },
        ticketSystem: {
            description: 'Communication system between vendors and retailers',
            purpose: 'Handle customer service, order issues, and general communication',
            location: 'Tickets section in dashboard'
        },
        emailNotifications: {
            description: 'Automated email notifications for important events',
            types: ['Order confirmations', 'Application updates', 'Ticket responses'],
            settings: 'Settings > Notifications'
        },
        spreadsheetImport: {
            description: 'Bulk product import via Excel/CSV files',
            applicableTo: 'vendors',
            supportedFormats: ['XLSX', 'CSV'],
            location: 'Products > Import'
        },

        thcProducts: {
            description: 'THC and Flower-derived CBD product management',
            applicableTo: 'vendors',
            requirements: [
                'Must indicate THC product sales intent in vendor application',
                'Application must be approved by admin',
                'Valid cannabis license required for public visibility',
                'Products can be created before approval but remain hidden'
            ],
            restrictions: {
                domestic: 'Can only sell THC products within their licensed state',
                international: 'Can sell THC products globally',
                brokers: 'Must provide seller license information for each THC product (license number, holder name, expiration date)'
            },
            troubleshooting: [
                'If THC product types are not available, check if THC sales was enabled in your application',
                'Contact support to update your application to include THC product sales',
                'THC products will not be publicly visible until admin approval',
                'Ensure your cannabis license is valid and not expired'
            ]
        }
    }
};

// Helper functions to retrieve knowledge
function getKnowledgeForRole(role) {
    return PLATFORM_KNOWLEDGE.roles[role] || null;
}

function getCommonTask(taskName) {
    return PLATFORM_KNOWLEDGE.commonTasks[taskName] || null;
}

function getTroubleshootingInfo(category) {
    return PLATFORM_KNOWLEDGE.troubleshooting[category] || null;
}

function getFeatureInfo(featureName) {
    return PLATFORM_KNOWLEDGE.features[featureName] || null;
}

function searchKnowledge(query) {
    const results = [];
    const searchTerm = query.toLowerCase();
    
    // Search in common tasks
    Object.entries(PLATFORM_KNOWLEDGE.commonTasks).forEach(([key, task]) => {
        if (key.toLowerCase().includes(searchTerm) || 
            task.steps.some(step => step.toLowerCase().includes(searchTerm))) {
            results.push({ type: 'task', key, data: task });
        }
    });
    
    // Search in troubleshooting
    Object.entries(PLATFORM_KNOWLEDGE.troubleshooting).forEach(([key, info]) => {
        if (key.toLowerCase().includes(searchTerm) || 
            info.some(item => item.toLowerCase().includes(searchTerm))) {
            results.push({ type: 'troubleshooting', key, data: info });
        }
    });
    
    // Search in features
    Object.entries(PLATFORM_KNOWLEDGE.features).forEach(([key, feature]) => {
        if (key.toLowerCase().includes(searchTerm) || 
            feature.description.toLowerCase().includes(searchTerm)) {
            results.push({ type: 'feature', key, data: feature });
        }
    });
    
    return results;
}

module.exports = {
    PLATFORM_KNOWLEDGE,
    getKnowledgeForRole,
    getCommonTask,
    getTroubleshootingInfo,
    getFeatureInfo,
    searchKnowledge
};
