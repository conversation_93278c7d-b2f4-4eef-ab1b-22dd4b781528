'use strict';

const mongoose = require('mongoose');
const config = require('../services/config');
const { FileSchema, RatingSchema, ParameterSchema, BatchSchema, VariantSchema, RankingSchema, ColorSchema, QuantityDiscountConditionSchema } = require('../services/util');
const passwordGenerator = require('password-generator');
const { preSaveGenerateUniqueField } = require('../services/util');
const constants = require('./constants');
const { ModelError } = require('../services/error');
const { ObjectId } = mongoose.Schema.Types;

const ProductSchema = new mongoose.Schema(
    {
        key: {
            type: String,
            systemLogLabel: 'Key',
            index: true
        },
        _vendor: {
            type: ObjectId,
            ref: 'Vendor',
            index: true
        },
        _category: {
            type: ObjectId,
            ref: 'Category',
            systemLogLabel: 'Category',
            required: true
        },
        _location: {
            type: ObjectId,
            systemLogLabel: 'Location',
            ref: 'Address'
        },
        _state: {
            type: ObjectId,
            ref: 'State',
            index: true
        },
        status: {
            type: String,
            enum: constants.PRODUCT.STATUSES,
            systemLogLabel: 'Status',
            required: true
        },
        type: {
            type: String,
            enum: constants.PRODUCT.TYPES,
            required: true,
            systemLogLabel: 'Type',
            index: true
        },
        isGrowing: {
            type: Boolean,
            default: false,
            systemLogLabel: 'Growing Product'
        },
        cannabisLicense: {
            type: Boolean,
            required: true,
            systemLogLabel: 'Cannabis Licence',
            default: false
        },
        mainSku: {
            type: String,
            index: true,
            systemLogLabel: 'Main Sku',
            required: true,
            unique: true
        },
        name: {
            type: String,
            index: true,
            systemLogLabel: 'Name',
            required: true
        },
        description: {
            type: String,
            systemLogLabel: 'Description'
        },
        strain: {
            type: String,
            systemLogLabel: 'Strain',
            enum: constants.PRODUCT.STRAINS
        },
        content: {
            thc: ParameterSchema('THC'),
            cbd: ParameterSchema('CBD'),
            cbn: ParameterSchema('CBN')
        },
        returnPolicy: {
            type: String,
            systemLogLabel: 'Return Policy'
        },
        nonRefundable: {
            type: Boolean,
            systemLogLabel: 'Non Refundable'
        },
        mainImage: {
            type: FileSchema,
            systemLogLabel: 'Main Image'
        },
        images: {
            type: [FileSchema],
            systemLogLabel: 'Images',
            default: []
        },
        files: {
            type: [FileSchema],
            systemLogLabel: 'Documents',
            default: []
        },
        effects: {
            Aroused: ParameterSchema('Aroused effect'),
            Creative: ParameterSchema('Creative effect'),
            Energetic: ParameterSchema('Energetic effect'),
            Euphoric: ParameterSchema('Euphoric effect'),
            Focused: ParameterSchema('Focused effect'),
            Giggly: ParameterSchema('Giggly effect'),
            Happy: ParameterSchema('Happy effect'),
            Hungry: ParameterSchema('Hungry effect'),
            Relaxed: ParameterSchema('Relaxed effect'),
            Sleepy: ParameterSchema('Sleepy effect'),
            Talkative: ParameterSchema('Talkative effect'),
            Tingly: ParameterSchema('Tingly effect'),
            Uplifted: ParameterSchema('Uplifted effect'),
            Anxious: ParameterSchema('Anxious effect'),
            Headache: ParameterSchema('Headache effect'),
            Paranoid: ParameterSchema('Paranoid effect')
        },
        medicalSymptoms: {
            type: [String],
            systemLogLabel: 'Medical symptom'
        },
        medicalConditions: {
            type: [String],
            systemLogLabel: 'Medical conditions'
        },
        flowers: {
            type: [String],
            systemLogLabel: 'Product flower'
        },
        environment: {
            type: String,
            systemLogLabel: 'Environment',
            enum: constants.PRODUCT.ENVIRONMENTS,
            default: constants.PRODUCT.ENVIRONMENT.N_A
        },
        price: {
            type: Number,
            systemLogLabel: 'Price'
        },
        lastPurchasedAt: Date,
        weights: {
            type: [String],
            systemLogLabel: 'Weights',
            default: []
        },
        colors: {
            type: [ColorSchema],
            systemLogLabel: 'Colors',
            default: []
        },
        sizing: {
            volumes: {
                type: [String],
                systemLogLabel: 'Sizing volumes',
                default: []
            },
            others: {
                type: [String],
                systemLogLabel: 'Sizing others',
                default: []
            }
        },
        custom1: {
            name: {
                type: String,
                systemLogLabel: 'Custom1 dimension name',
                default: 'custom1'
            },
            values: {
                type: [String],
                systemLogLabel: 'Custom1 dimension values',
                default: []
            }
        },
        custom2: {
            name: {
                type: String,
                systemLogLabel: 'Custom2 dimension name',
                default: 'custom2'
            },
            values: {
                type: [String],
                systemLogLabel: 'Custom2 dimension values',
                default: []
            }
        },
        batches: {
            type: [BatchSchema],
            systemLogLabel: 'Batches',
            default: []
        },
        value: { // TODO: fixme: whats is it ?
            type: Number,
            index: true
        },
        deliveryDays: {
            type: [Number],
            systemLogLabel: 'Delivery days',
            set: value => Array.isArray(value) ? value.slice(0, 2) : []
        },
        rating: {
            type: RatingSchema,
            default: {}
        },
        quantityDiscount: {
            type: {
                type: String,
                systemLogLabel: 'Quantity discount type',
                enum: ['fixedPerOrder', 'fixedPerItem', 'percentPerItem']
            },
            conditions: {
                type: [QuantityDiscountConditionSchema],
                systemLogLabel: 'Quantity discount',
                default: []
            }
        },
        _brokerLicense: {
            type: ObjectId,
            ref: 'BrokerLicense',
            systemLogLabel: 'Broker License Reference',
            index: true
        },
        deletedAt: {
            type: Date,
            index: true
        },
        quantitySold: {
            type: Number,
            index: true,
            default: 0
        },
        searchViews: {
            type: Number,
            default: 0
        },
        productViews: {
            type: Number,
            default: 0
        },
        productBuyClicks: {
            type: Number,
            default: 0
        },
        metrcPckgID: {
            type: Number,
            index: true,
            default: 0
        },
        metrcPckgLabel: {
            type: String,
            index: true,
            default: ''
        },
        metrcSync: {
            type: Boolean,
            required: true,
            systemLogLabel: 'Metrc Sync',
            default: false
        },
        metrcSyncDate: {
            type: String,
            default: ''
        },
        _ranking: RankingSchema,
        // Remove these fields after migration 47
        variants: {
            type: [VariantSchema],
            systemLogLabel: 'Variants'
        }
    },
    { timestamps: true }
);

ProductSchema
    .index({ name: 'text' })
    .static('incrementSearchViews', incrementSearchViews)
    .method('incrementProductViews', incrementProductViews)
    .method('incrementProductBuyClicks', incrementProductBuyClicks)
    .static('create', create)
    .method('formatPublic', formatPublic)
    .method('formatSource', formatSource)
    .method('formatAdmin', formatAdmin)

    .method('remove', removeProduct, { suppressWarning: true }) // added to supress warning. (This isn't actually deleting, it just marks it as deleted)
    // enable this after migration 47
    // .pre('validate', preValidateSku) 
    .pre('validate', preValidateCannabisLicense)
    // .pre('save', preSaveGeneralPrice)
    .pre('save', preSaveCalcRanking)
    .pre('save', preSaveGenerateUniqueField('key', generateKey));

const Product = mongoose.model('Product', ProductSchema);

function generateKey() {
    return passwordGenerator(8, false, /[0-9A-HJ-NP-Z]/, 'P');
}

async function incrementSearchViews(productIds = []) {
    await Product.updateMany({ _id: { $in: productIds } }, { $inc: { searchViews: 1 } });
}
async function incrementProductViews() {
    await Product.updateOne({ _id: this._id }, { $inc: { productViews: 1 } });
}
async function incrementProductBuyClicks() {
    await Product.updateOne({ _id: this._id }, { $inc: { productBuyClicks: 1 } });
}

async function create(payload, vendor) {
    
    const status = vendor._application && constants.APPLICATION.STATUS.ACCEPTED === vendor._application.status ? payload.status || constants.PRODUCT.STATUS.INACTIVE : constants.PRODUCT.STATUS.INACTIVE;
    const product = new Product(Object.assign(payload, {
        _vendor: vendor,
        _state: vendor._state,
        status
    }));
    await product.save();
    return product;
}

async function preValidateSku() {
    // eslint-disable-next-line
    // Validate sub-sku beforehand
    const skus = [];
    (this.batches || []).forEach(batch => {
        (batch.variants || []).forEach(variant => {
            skus.push(variant.sku);
        })
    });

    let query = {
        _vendor: this._vendor,
        _id: { $ne: this._id },
        deletedAt: { $exists: false },
        'batches.variants.sku': skus
    };

    let existsProducts = await Product.find(query);
    const errors = {};
    existsProducts.forEach(product => {
        (product.batches || []).forEach(productBatch => {
            (productBatch.variants || []).forEach(productVariant => {
                (this.batches || []).forEach(batch => {
                    (batch.variants || []).forEach(variant => {
                        if (productVariant.sku && productVariant.sku === variant.sku) {
                            errors['sku-' + uniqueKey(variant)] = 'Sku must be unique string';
                        }
                    });
                });
            });
        });
    });

    // Check if products have same main sku
    query = {
        _vendor: this._vendor,
        _id: { $ne: this._id },
        deletedAt: { $exists: false },
        mainSku: this.mainSku
    };

    existsProducts = await Product.find(query);
    if (existsProducts.length) {
        errors.sku = 'Main sku must be unique string';
    }

    if (Object.keys(errors).length) {        
        throw new ModelError('', errors);
    }
}

async function preValidateCannabisLicense() {
    this.cannabisLicense = [constants.PRODUCT.TYPE.THC, constants.PRODUCT.TYPE.FLOWED_CBD].includes(this.type);
}

function uniqueKey({ size = '', weight = '', color = '', custom1 = '', custom2 = '' } = {}) {
    let key = '';
    if (size) {
        key += size;
    }
    if (weight) {
        key += weight;
    }
    if (color && color.name && color.hex) {
        key += color.name + color.hex;
    }
    if (custom1) {
        key += custom1;
    }
    if (custom2) {
        key += custom2;
    }
    return key;
}

// TODO: Set price as main sku's price
// function preSaveGeneralPrice(done) {    
//     this.price = this.variants && this.variants[0] && this.variants[0].price || 0;
//     return done();
// }

async function preSaveCalcRanking() {
    const { Vendor } = mongoose.models;

    function calcProductQuality(product) {
        let sum = 0;
        // TODO: calculate ranking with main sku's price
        // if (product.variants && product.variants[0] && product.variants[0].mainImage && product.variants[0].mainImage.uuid) {
        //     sum += 4;
        // }
        if ((product.images || []).length) {
            sum += 1;
        }
        // TODO: remove calculating ranking by variants
        // if ((product.variants || []).length > 1) {
        //     sum += 1;
        // }
        if ((product.batches || []).length > 1) {
            sum += 1;
        }
        if (product.returnPolicy && product.returnPolicy.length > 1) {
            sum += 1;
        }
        if (product.description && product.description.length > 100) {
            sum += 1;
        }
        if (Math.max.apply(null, Object.values(product.effects)) > 0 || product.flowers.length || product.medicalConditions.length || product.medicalSymptoms.length) {
            sum += 1;
        }
        if (product._location || product.deliveryDays && (product.deliveryDays[0] || product.deliveryDays[1])) {
            sum += 1;
        }
        return sum.toFixed(2);
    }

    function calcProductRank({ value = 0, count = 0 } = {}) {
        if (count >= 100) {
            return value * 2;
        }
        if (count < 5) {
            return value * 2 * 0.3;
        }
        return (value * 2 * Math.log(count) / Math.log(100)).toFixed(2);
    }

    function calcReturnScore(vendor) {
        switch (vendor.returnProbability) {
            case constants.VENDOR.RETURN_PROBABLY.VERY_HIGH:
                return 10;
            case constants.VENDOR.RETURN_PROBABLY.HIGH:
                return 5;
            case constants.VENDOR.RETURN_PROBABLY.MEDIUM:
                return 3;
            default:
                return 0;
        }
    }

    const vendor = await Vendor.findOne({ _id: this._vendor }, 'returnProbability rating').lean();

    this._ranking = {};
    this._ranking.productQuality = calcProductQuality(this);
    this._ranking.productRank = calcProductRank(this.rating);
    this._ranking.vendorRank = calcProductRank(vendor.rating);
    this._ranking.returnScore = calcReturnScore(vendor);
    this._ranking.total = (4 * this._ranking.productQuality + 3 * this._ranking.productRank + 2 * this._ranking.vendorRank + this._ranking.returnScore).toFixed(2);
}


function formatPublic() {
    return {
        key: this.key,
        status: this.status,
        vendor: this._vendor.formatPublic && this._vendor.formatPublic(),
        cannabisLicense: this.cannabisLicense,
        isGrowing: this.isGrowing,
        type: this.type,
        name: this.name,
        description: this.description,
        category: this._category,
        strain: this.strain,
        content: {
            thc: this.content.thc,
            cbd: this.content.cbd,
            cbn: this.content.cbn
        },
        returnPolicy: this.returnPolicy,
        nonRefundable: this.nonRefundable,
        images: (this.images || []).map(image => image.formatFullFile()),
        files: (this.files || []).map(file => file.formatFullFile()),
        effects: {
            Aroused: this.effects.Aroused,
            Creative: this.effects.Creative,
            Energetic: this.effects.Energetic,
            Euphoric: this.effects.Euphoric,
            Focused: this.effects.Focused,
            Giggly: this.effects.Giggly,
            Happy: this.effects.Happy,
            Hungry: this.effects.Hungry,
            Relaxed: this.effects.Relaxed,
            Sleepy: this.effects.Sleepy,
            Talkative: this.effects.Talkative,
            Tingly: this.effects.Tingly,
            Uplifted: this.effects.Uplifted,
            Anxious: this.effects.Anxious,
            Headache: this.effects.Headache,
            Paranoid: this.effects.Paranoid
        },
        medicalSymptoms: this.medicalSymptoms,
        medicalConditions: this.medicalConditions,
        flowers: this.flowers,
        environment: this.environment,
        weights: this.weights,
        colors: (this.colors || []).map(color => ({
            name: color.name,
            hex: color.hex
        })),
        sizing: {
            volumes: this.sizing.volumes,
            others: this.sizing.others
        },
        custom1: {
            name: this.custom1.name,
            values: this.custom1.values
        },
        custom2: {
            name: this.custom2.name,
            values: this.custom2.values
        },
        batches: this.batches || [],
        location: this._location && this._location._id || this._location,
        value: this.value,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt,
        deliveryDays: this.deliveryDays,
        rating: this.rating,
        quantityDiscount: {
            type: this.quantityDiscount.type,
            conditions: (this.quantityDiscount.conditions || []).map(cond => ({
                minQuantity: cond.minQuantity,
                condition: cond.condition,
                maxQuantity: cond.maxQuantity,
                discount: cond.discount
            }))
        },
        metrcSync: this.metrcSync,
        metrcPckgID: this.metrcPckgID,
        metrcSyncDate: this.metrcSyncDate,
        mainSku: this.mainSku,
        mainImage: this.mainImage
    };
}

function formatSource() {
    return {
        ...formatPublic.call(this),
        deletedAt: this.deletedAt
    };
}

function formatAdmin() {
    return {
        ...formatSource.call(this),
        deletedAt: this.deletedAt,
        state: this._state,
        searchViews: this.searchViews,
        productViews: this.productViews,
        productBuyClicks: this.productBuyClicks,
        quantitySold: this.quantitySold,
        ranking: this._ranking.formatPublic(),
        vendor: this._vendor.formatSource()
    };
}

// function formatSource() {
//     return {
//         ...formatPublic.call(this),
//         variants: (this.variants || []).map(variant => variant.formatSource()),
//         deletedAt: this.deletedAt
//     };
// }

async function removeProduct() {
    this.deletedAt = new Date();
    this.status = constants.PRODUCT.STATUS.DELETED;
    await this.save();
}

// async function source() {
//     ['test', 'this', 'that'];
// }


