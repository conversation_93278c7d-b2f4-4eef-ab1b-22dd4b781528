'use strict';

const crypto = require('crypto');
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Schema.Types;
const constants = require('./constants');
const emailService = require('../services/email');
const { AddressSchema, FileSchema, preSaveGenerateUniqueField } = require('../services/util');
const passwordGenerator = require('password-generator');
const { DateTime } = require('luxon');
const config = require('../services/config');

const EMPTY_EIN_BUFFER = Buffer.from([0, 0, 0]);

const ApplicationSchema = new mongoose.Schema(
    {
        key: {
            type: String,
            systemLogLabel: 'Key',
            index: true
        },
        _vendor: {
            type: ObjectId,
            ref: 'Vendor',
            index: true
        },
        _retailer: {
            type: ObjectId,
            ref: 'Retailer',
            index: true
        },
        _state: {
            type: ObjectId,
            ref: 'State',
            index: true
        },
        status: {
            type: String,
            systemLogLabel: 'Status',
            enum: constants.APPLICATION.STATUSES,
            required: true
        },
        type: {
            type: String,
            systemLogLabel: 'Type',
            enum: constants.APPLICATION.TYPES
        },
        submittedAt: Date,
        acceptedAt: {
            type: Date,
            systemLogLabel: 'Accepted At',
            index: true
        },
        expiredAt: {
            type: Date,
            systemLogLabel: 'Expired At',
            index: true
        },
        processedAt: {
            type: Date,
            systemLogLabel: 'Processed At'
        },
        processedBy: {
            type: String,
            systemLogLabel: 'Processed By'
        },
        processedIp: String,
        businessName: {
            type: String,
            systemLogLabel: 'Business Name',
            index: true,
            required: true
        },
        tradeName: {
            type: String,
            systemLogLabel: 'Trade Name',
            index: true,
            required: true
        },
        ein: {
            type: Buffer,
            systemLogLabel: 'EIN/Tax id',
            get: decryptEin,
            set: encryptEin,
            required: true
        },
        legalAddress: {
            type: AddressSchema,
            systemLogLabel: 'Legal Address',
            required: true
        },
        fullName: {
            type: String,
            systemLogLabel: 'Full Name',
            index: true,
            required: true
        },
        phone: {
            type: String,
            systemLogLabel: 'Phone',
            index: true,
            required: true
        },
        license: {
            cannabis: {
                type: Boolean,
                systemLogLabel: 'Licence'
            },
            status: {
                type: Boolean,
                systemLogLabel: 'Status'
            },
            state: {
                type: ObjectId,
                ref: 'State',
                systemLogLable: 'Regulatory State'
            },
            regulator: {
                type: ObjectId,
                ref: 'RegulatoryBody',
                systemLogLable: 'Regulatory Body'
            },
            number: {
                type: String,
                systemLogLabel: 'Number',
                required: true
            },
            name: {
                type: String,
                systemLogLabel: 'Name'
            },
            licenseType: {
                type: String,
                systemLogLabel: 'Type',
                required: true
            },
            businessName: {
                type: String,
                systemLogLabel: 'Business Name'
            },
            county: {
                type: String,
                systemLogLabel: 'County'
            }
        },
        files: {
            type: [FileSchema],
            systemLogLabel: 'Documents'
        },
        agreeTermsOfServiceAndPrivacyPolicy: {
            type: Boolean,
            systemLogLabel: 'Agree Terms Of Service And Privacy Policy',
            default: false
        },
        agreeUserFeesAndPaymentPolicy: {
            type: Boolean,
            systemLogLabel: 'Agree User Fees And Payment Policy',
            default: false
        },
        agreeReturnPolicy: {
            type: Boolean,
            systemLogLabel: 'Agree Return Policy',
            default: false
        },
        description: {
            type: String,
            systemLogLabel: 'Description',
            required: true
        },
        monthlySales: {
            type: String,
            systemLogLabel: 'Average monthly sales',
            required: true
        },
        proofOfAddress: {
            type: FileSchema,
            systemLogLabel: 'Proof of address'
        },
        personalId: {
            type: FileSchema,
            systemLogLabel: 'Personal id '
        },
        adminNotes: {
            type: String,
            systemLogLabel: 'Admin Notes'
        }
    },
    { timestamps: true }
);

ApplicationSchema
    .static('getForVendor', getForVendor)
    .static('updateForVendor', updateForVendor)
    .static('createForVendor', createForVendor)
    .static('getForRetailer', getForRetailer)
    .static('updateForRetailer', updateForRetailer)
    .static('createForRetailer', createForRetailer)
    .static('emptyEin', emptyEin)
    .method('decline', decline)
    .method('approve', approve)
    .method('formatPublic', formatPublic)
    .method('formatSource', formatSource)
    .method('formatAdmin', formatAdmin)
    .pre('save', preSaveGenerateUniqueField('key', generateKey));

ApplicationSchema.pre('save', async function (next) {
    const stateId = this._state;
    const State = mongoose.model('State');
    const stateQuery = State.findById(stateId);
    await stateQuery.populate('requiredDocuments');

    stateQuery.exec((err, state) => {
        if (err) {
            return next(err);
        }

        const requiredDocuments = state.requiredDocuments || [];

        // Check that all required documents are present
        for (const doc of requiredDocuments) {
            const matchingFile = this.files.find(file => file.docName === doc.name);
            if (!matchingFile) {
                return next(new Error(`Missing required document: ${doc.name}`));
            }
            if (matchingFile.expirationDate && matchingFile.expirationDate <= new Date()) {
                return next(new Error(`Expired document: ${doc.name}`));
            }
        }

        next();
    });
});

const Application = mongoose.model('Application', ApplicationSchema);

function generateKey() {
    return passwordGenerator(8, false, /[0-9A-HJ-NP-Z]/, 'A');
}

async function getForVendor(vendor) {
    const application = await Application.findOne({ _id: vendor._application });
    return application;
}

async function updateForVendor(data) {
    let application;

    // If vendor has an application ID, try to update it
    if (data.vendor._application) {
        application = await Application.findOneAndUpdate(
            { _id: data.vendor._application },
            {
                ...data,
                status: data.forDraft ? constants.APPLICATION.STATUS.DRAFT : constants.APPLICATION.STATUS.PENDING,
                updatedAt: new Date()
            },
            { new: true }
        );
    }

    // If no application found or vendor has no application ID, create a new one
    if (!application) {
        application = new Application({
            ...data,
            _vendor: data.vendor,
            _state: data.vendor._state,
            type: constants.APPLICATION.TYPE.VENDOR,
            status: data.forDraft ? constants.APPLICATION.STATUS.DRAFT : constants.APPLICATION.STATUS.PENDING,
            submittedAt: new Date()
        });
        await application.save();
    }

    data.vendor.set({
        _application: application._id,
        lastAgreeTermsAt: new Date()
    });
    await data.vendor.save();

    // Send email notification to admins about the application update
    // Only send if it's not a draft (drafts are auto-saved frequently)
    if (!data.forDraft) {
        try {
            const {vendorApplicationUpdatedEmail} = require('../services/email/vendor');
            await vendorApplicationUpdatedEmail({application});
        } catch (error) {
            console.error('Failed to send vendor application updated email:', error);
            // Don't throw error - email failure shouldn't block the update
        }
    }

    return application;
}

async function createForVendor(data) {
    const application = new Application({
        ...data,
        _vendor: data.vendor,
        _state: data.vendor._state,
        type: constants.APPLICATION.TYPE.VENDOR,
        status: data.isDraft ? constants.APPLICATION.STATUS.DRAFT : constants.APPLICATION.STATUS.PENDING,
        submittedAt: new Date
    });
    await application.save();

    data.vendor.set({
        _application: application._id,
        lastAgreeTermsAt: new Date()
    });
    await data.vendor.save();

    await emailService.create.vendorApplicationSubmittedEmail({ application });
    return application;
}

async function getForRetailer(retailer) {
    const application = await Application.findOne({ _id: retailer._application });
    return application;
}

async function updateForRetailer(data) {
    let application;

    // If retailer has an application ID, try to update it
    if (data.retailer._application) {
        application = await Application.findOneAndUpdate(
            { _id: data.retailer._application },
            {
                ...data,
                status: data.forDraft ? constants.APPLICATION.STATUS.DRAFT : constants.APPLICATION.STATUS.PENDING,
                updatedAt: new Date()
            },
            { new: true }
        );
    }

    // If no application found or retailer has no application ID, create a new one
    if (!application) {
        application = new Application({
            ...data,
            _retailer: data.retailer,
            _state: data.retailer._state,
            type: constants.APPLICATION.TYPE.RETAILER,
            status: data.forDraft ? constants.APPLICATION.STATUS.DRAFT : constants.APPLICATION.STATUS.PENDING,
            submittedAt: new Date()
        });
        await application.save();
    }

    data.retailer.set({
        _application: application._id,
        lastAgreeTermsAt: new Date()
    });
    await data.retailer.save();

    // Send email notification to admins about the application update
    // Only send if it's not a draft (drafts are auto-saved frequently)
    if (!data.forDraft) {
        try {
            const {retailerApplicationUpdatedEmail} = require('../services/email/retailer');
            await retailerApplicationUpdatedEmail({application});
        } catch (error) {
            console.error('Failed to send retailer application updated email:', error);
            // Don't throw error - email failure shouldn't block the update
        }
    }

    return application;
}

async function createForRetailer(data) {
    const application = new Application({
        ...data,
        _retailer: data.retailer,
        _state: data.retailer._state,
        type: constants.APPLICATION.TYPE.RETAILER,
        status: data.isDraft ? constants.APPLICATION.STATUS.DRAFT : constants.APPLICATION.STATUS.PENDING,
        submittedAt: new Date
    });
    await application.save();

    data.retailer.set({
        _application: application._id,
        lastAgreeTermsAt: new Date()
    });
    await data.retailer.save();

    await emailService.create.retailerApplicationSubmittedEmail({ application });
    return application;
}

async function decline() {
    this.status = constants.APPLICATION.STATUS.DECLINED;
    this.declinedAt = new Date();
    this.processedAt = new Date();

    await this.save();
    if (this.type === constants.APPLICATION.TYPE.VENDOR) {
        await emailService.create.vendorApplicationDeclinedEmail({ application: this });
    }
    else if (this.type === constants.APPLICATION.TYPE.RETAILER) {
        await emailService.create.retailerApplicationDeclinedEmail({ application: this });
    }
}
async function approve() {
    const now = new Date();
    this.status = constants.APPLICATION.STATUS.ACCEPTED;
    this.processedAt = now;
    this.acceptedAt = now;
    this.expiredAt = DateTime
        .fromJSDate(now)
        .plus({ years: 1 })
        .startOf('day')
        .toJSDate();
    await this.save();

    // Auto-enable THC products for vendors if they applied for cannabis license
    if (this.type === constants.APPLICATION.TYPE.VENDOR && this.license && this.license.cannabis) {
        const { Vendor } = require('mongoose').models;
        const vendor = await Vendor.findById(this._vendor);
        if (vendor && !vendor.showThc) {
            vendor.set({ showThc: true });
            await vendor.save();
        }
    }

    // Auto-enable THC products for retailers if they applied for cannabis license
    if (this.type === constants.APPLICATION.TYPE.RETAILER && this.license && this.license.cannabis) {
        const { Retailer } = require('mongoose').models;
        const retailer = await Retailer.findById(this._retailer);
        if (retailer && !retailer.showThc) {
            retailer.set({ showThc: true });
            await retailer.save();
        }
    }

    if (this.type === constants.APPLICATION.TYPE.VENDOR) {
        await emailService.create.vendorApplicationAcceptedEmail({ application: this });
    }
    else if (this.type === constants.APPLICATION.TYPE.RETAILER) {
        await emailService.create.retailerApplicationAcceptedEmail({ application: this });
    }
}

function formatPublic() {
    return {
        key: this.key,
        status: this.status,
        submittedAt: this.submittedAt,
        acceptedAt: this.acceptedAt,
        expiredAt: this.expiredAt,
        processedAt: this.processedAt,
        businessName: this.businessName,
        tradeName: this.tradeName,
        ein: this.ein,
        legalAddress: this.legalAddress,
        fullName: this.fullName,
        phone: this.phone,
        license: {
            cannabis: this.license.cannabis
        },
        files: (this.files || []).map(image => image.formatFullFile && image.formatFullFile()),
        adminNotes: this.adminNotes
    };
}

function formatSource() {
    return {
        ...formatPublic.call(this),
        type: this.type,
        vendor: this._vendor && this._vendor.formatPublic(),
        retailer: this._retailer && this._retailer.formatPublic(),
        submittedAt: this.submittedAt,
        processedAt: this.processedAt,
        businessName: this.businessName,
        tradeName: this.tradeName,
        ein: this.ein,
        legalAddress: this.legalAddress,
        fullName: this.fullName,
        phone: this.phone,
        license: {
            cannabis: this.license.cannabis,
            number: this.license.number,
            name: this.license.name,
            licenseType: this.license.licenseType,
            type: this.license.type,
            businessName: this.license.businessName,
            county: this.license.county
        },
        processedBy: this.processedBy,
        processedIp: this.processedIp,
        adminNotes: this.adminNotes,
        files: (this.files || []).map(image => image.formatFullFile && image.formatFullFile()),
        description: this.description,
        monthlySales: this.monthlySales,
        proofOfAddress: this.proofOfAddress && this.proofOfAddress.formatFullFile && this.proofOfAddress.formatFullFile(),
        personalId: this.personalId && this.personalId.formatFullFile && this.personalId.formatFullFile(),
        agreeTermsOfServiceAndPrivacyPolicy: this.agreeTermsOfServiceAndPrivacyPolicy,
        agreeUserFeesAndPaymentPolicy: this.agreeUserFeesAndPaymentPolicy,
        agreeReturnPolicy: this.agreeReturnPolicy
    };
}
function formatAdmin() {
    return {
        ...formatSource.call(this),
        state: this._state,
        adminNotes: this.adminNotes
    };
}

function encryptEin(value) {
    if (value === '') {
        return EMPTY_EIN_BUFFER;
    }
    return crypto.publicEncrypt(config.ccStore.public, new Buffer(value));
}
function decryptEin(data) {
    if (Buffer.compare(data, EMPTY_EIN_BUFFER) === 0) {
        return '';
    }
    return crypto.privateDecrypt(config.ccStore.private, data).toString();
}
function emptyEin() {
    return EMPTY_EIN_BUFFER;
}
