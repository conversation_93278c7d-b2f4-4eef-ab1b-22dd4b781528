'use strict';

const PRODUCT_TYPE_THC = 'thc';
const PRODUCT_TYPE_FLOWED_CBD = 'flower_derived_cbd';
const PRODUCT_TYPE_HEMP_CBD = 'hemp_derived_cbd';
const PRODUCT_TYPE_ANCILLARY = 'ancillary';
const PRODUCT_TYPES = [
    PRODUCT_TYPE_THC,
    PRODUCT_TYPE_FLOWED_CBD,
    PRODUCT_TYPE_HEMP_CBD,
    PRODUCT_TYPE_ANCILLARY
];

const PRODUCT_ENVIRONMENT_SUNGROWN = 'sungrown';
const PRODUCT_ENVIRONMENT_INDOORS = 'indoors';
const PRODUCT_ENVIRONMENT_GREENHOUSE = 'greenhouse';
const PRODUCT_ENVIRONMENT_N_A = 'n/a';
const PRODUCT_ENVIRONMENTS = [PRODUCT_ENVIRONMENT_SUNGROWN, PRODUCT_ENVIRONMENT_INDOORS, PRODUCT_ENVIRONMENT_GREENHOUSE, PRODUCT_ENVIRONMENT_N_A];

const USER_ROLE_VENDOR = 'vendor';
const USER_ROLE_RETAILER = 'retailer';
// const USER_ROLE_SHIPPER = 'shipper';
// const USER_ROLES = [USER_ROLE_VENDOR, USER_ROLE_RETAILER, USER_ROLE_SHIPPER];
const USER_ROLES = [USER_ROLE_VENDOR, USER_ROLE_RETAILER];

const EMAIL_TYPE_RESTORE_PASSWORD = 'userPasswordRestore';
const EMAIL_TYPE_SIGNUP = 'userSignup';
const EMAIL_TYPE_CHANGE_EMAIL = 'userEmailChangeRequest';
const EMAIL_TYPE_VERIFY_EMAIL = 'userEmailVerifyRequest';
const EMAIL_TYPE_TEAMMATE_INVITE = 'teammateInviteRequest';
const EMAIL_TYPE_VENDOR_APPLICATION_DRAFT = 'vendorApplicationDraft';
const EMAIL_TYPE_VENDOR_APPLICATION_SUBMITTED = 'vendorApplicationSubmitted';
const EMAIL_TYPE_VENDOR_APPLICATION_UPDATED = 'vendorApplicationUpdated';
const EMAIL_TYPE_VENDOR_APPLICATION_ACCEPTED = 'vendorApplicationAccepted';
const EMAIL_TYPE_VENDOR_APPLICATION_DECLINED = 'adminApplicationDeclined';
const EMAIL_TYPE_RETAILER_APPLICATION_DRAFT = 'retailerApplicationDraft';
const EMAIL_TYPE_RETAILER_APPLICATION_SUBMITTED = 'retailerApplicationSubmitted';
const EMAIL_TYPE_RETAILER_APPLICATION_UPDATED = 'retailerApplicationUpdated';
const EMAIL_TYPE_RETAILER_APPLICATION_ACCEPTED = 'retailerApplicationAccepted';
const EMAIL_TYPE_RETAILER_APPLICATION_DECLINED = 'adminApplicationDeclined';

const EMAIL_TYPE_RETAILER_ORDER_MISSING_DOCUMENTS = 'retailerOrderMissingDocuments';
const EMAIL_TYPE_RETAILER_ORDER_CREATED = 'retailerOrderCreated';
const EMAIL_TYPE_RETAILER_ORDER_SHIPPED = 'retailerOrderShipped';
const EMAIL_TYPE_RETAILER_ORDER_DELIVERED = 'retailerOrderDelivered';
const EMAIL_TYPE_RETAILER_ORDER_REJECTED = 'retailerOrderRejected';

const EMAIL_TYPE_RETAILER_TICKET_MESSAGE = 'retailerTicketMessage';
const EMAIL_TYPE_VENDOR_TICKET_MESSAGE = 'vendorTicketMessage';

const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_PERMIT_DOC = 'retailerPermitDocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_LOI_DOC = 'retailerLOIdocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_NNF_DOC = 'vendorNNFdocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_PERMIT_DOC = 'vendorPermitDocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_SHIPPING_DOC = 'vendorShippingDocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_DELIVERY_DOC = 'retailerDeliveryDocument';

const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_RETAILER = 'retailerOrderDocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_VENDOR = 'vendorOrderDocument';
const EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR = 'retailerAndVendorDocument';

const EMAIL_TYPE_VENDOR_NEW_ORDER = 'vendorNewOrder';
const EMAIL_TYPE_VENDOR_ORDER_DELIVERED = 'vendorOrderDelivered';
const EMAIL_TYPE_VENDOR_ORDER_FUNDS_RELEASED = 'vendorOrderFundsReleased';

const EMAIL_TYPE_ADMIN_NEW_ORDER = 'adminNewOrder';
const EMAIL_TYPE_ADMIN_NEW_SIGNUP = 'adminNewSignup';

const EMAIL_TYPES = [
    EMAIL_TYPE_RESTORE_PASSWORD, EMAIL_TYPE_SIGNUP, EMAIL_TYPE_CHANGE_EMAIL, EMAIL_TYPE_VERIFY_EMAIL, EMAIL_TYPE_TEAMMATE_INVITE,
    EMAIL_TYPE_VENDOR_APPLICATION_DRAFT, EMAIL_TYPE_VENDOR_APPLICATION_SUBMITTED, EMAIL_TYPE_VENDOR_APPLICATION_UPDATED, EMAIL_TYPE_VENDOR_APPLICATION_ACCEPTED, EMAIL_TYPE_VENDOR_APPLICATION_DECLINED,
    EMAIL_TYPE_RETAILER_APPLICATION_DRAFT, EMAIL_TYPE_RETAILER_APPLICATION_SUBMITTED, EMAIL_TYPE_RETAILER_APPLICATION_UPDATED, EMAIL_TYPE_RETAILER_APPLICATION_ACCEPTED, EMAIL_TYPE_RETAILER_APPLICATION_DECLINED,

    EMAIL_TYPE_RETAILER_ORDER_MISSING_DOCUMENTS, EMAIL_TYPE_RETAILER_ORDER_CREATED, EMAIL_TYPE_RETAILER_ORDER_SHIPPED, EMAIL_TYPE_RETAILER_ORDER_DELIVERED, EMAIL_TYPE_RETAILER_ORDER_REJECTED,

    EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_PERMIT_DOC, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_LOI_DOC, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_NNF_DOC, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_PERMIT_DOC, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_SHIPPING_DOC, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_DELIVERY_DOC,

    EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_RETAILER, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_VENDOR, EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR,

    EMAIL_TYPE_VENDOR_NEW_ORDER, EMAIL_TYPE_VENDOR_ORDER_DELIVERED, EMAIL_TYPE_VENDOR_ORDER_FUNDS_RELEASED,

    EMAIL_TYPE_RETAILER_TICKET_MESSAGE, EMAIL_TYPE_VENDOR_TICKET_MESSAGE,

    EMAIL_TYPE_ADMIN_NEW_ORDER,
    EMAIL_TYPE_ADMIN_NEW_SIGNUP
];

const EMAIL_TEMPLATE_RESTORE_PASSWORD = 'canideal-restore-password';
const EMAIL_TEMPLATE_SIGNUP = 'canideal-org-signup';
const EMAIL_TEMPLATE_CHANGE_EMAIL = 'canideal-change-email';
const EMAIL_TEMPLATE_VERIFY_EMAIL = 'canideal-verify-email';
const EMAIL_TEMPLATE_TEAMMATE_INVITE = 'canideal-teammate-invite';
const EMAIL_TEMPLATE_VENDOR_APPLICATION_Draft = 'eb-vendor-not-applied';
const EMAIL_TEMPLATE_VENDOR_APPLICATION_SUBMITTED = 'canideal-vendor-application-submitted';
const EMAIL_TEMPLATE_VENDOR_APPLICATION_UPDATED = 'canideal-vendor-application-updated';
const EMAIL_TEMPLATE_VENDOR_APPLICATION_ACCEPTED = 'canideal-vendor-application-accepted';
const EMAIL_TEMPLATE_VENDOR_APPLICATION_DECLINED = 'canideal-application-declined';
const EMAIL_TEMPLATE_RETAILER_APPLICATION_Draft = 'eb-retailer-not-applied';
const EMAIL_TEMPLATE_RETAILER_APPLICATION_SUBMITTED = 'canideal-retailer-application-submitted';
const EMAIL_TEMPLATE_RETAILER_APPLICATION_UPDATED = 'canideal-retailer-application-updated';
const EMAIL_TEMPLATE_RETAILER_APPLICATION_ACCEPTED = 'canideal-retailer-application-accepted';
const EMAIL_TEMPLATE_RETAILER_APPLICATION_DECLINED = 'canideal-application-declined';

const EMAIL_TEMPLATE_RETAILER_ORDER_MISSING_DOCUMENTS = 'canideal-retailer-order-missing-documents';
const EMAIL_TEMPLATE_VENDOR_ORDER_MISSING_DOCUMENTS = 'canideal-vendor-order-missing-documents';
const EMAIL_TEMPLATE_RETAILER_ORDER_CREATED = 'canideal-retailer-order-created';
const EMAIL_TEMPLATE_RETAILER_ORDER_SHIPPED = 'canideal-retailer-order-shipped';
const EMAIL_TEMPLATE_RETAILER_ORDER_DELIVERED = 'canideal-retailer-order-delivered';
const EMAIL_TEMPLATE_RETAILER_ORDER_REJECTED = 'canideal-retailer-order-rejected';

const EMAIL_TEMPLATE_VENDOR_NEW_ORDER = 'canideal-vendor-new-order';
const EMAIL_TEMPLATE_VENDOR_ORDER_FUNDS_RELEASED = 'canideal-vendor-order-funds-released';
const EMAIL_TEMPLATE_VENDOR_ORDER_DELIVERED = 'canideal-vendor-order-delivered';
const EMAIL_TEMPLATE_ADMIN_NEW_ORDER = 'canideal-admin-new-order';
const EMAIL_TEMPLATE_ADMIN_NEW_SIGNUP = 'canideal-admin-new-signup';

const EMAIL_TEMPLATE_RETAILER_TICKET_MESSAGE = 'canideal-retailer-ticket-message';
const EMAIL_TEMPLATE_VENDOR_TICKET_MESSAGE = 'canideal-vendor-ticket-message';

const EMAIL_TEMPLATES = [
    EMAIL_TEMPLATE_RESTORE_PASSWORD, EMAIL_TEMPLATE_SIGNUP, EMAIL_TEMPLATE_CHANGE_EMAIL, EMAIL_TEMPLATE_VERIFY_EMAIL, EMAIL_TEMPLATE_TEAMMATE_INVITE,
    EMAIL_TEMPLATE_VENDOR_APPLICATION_Draft, EMAIL_TEMPLATE_VENDOR_APPLICATION_SUBMITTED, EMAIL_TEMPLATE_VENDOR_APPLICATION_UPDATED, EMAIL_TEMPLATE_VENDOR_APPLICATION_ACCEPTED, EMAIL_TEMPLATE_VENDOR_APPLICATION_DECLINED,
    EMAIL_TEMPLATE_RETAILER_APPLICATION_Draft, EMAIL_TEMPLATE_RETAILER_APPLICATION_SUBMITTED, EMAIL_TEMPLATE_RETAILER_APPLICATION_UPDATED, EMAIL_TEMPLATE_RETAILER_APPLICATION_ACCEPTED, EMAIL_TEMPLATE_RETAILER_APPLICATION_DECLINED,

    EMAIL_TEMPLATE_RETAILER_ORDER_MISSING_DOCUMENTS, EMAIL_TEMPLATE_VENDOR_ORDER_MISSING_DOCUMENTS, EMAIL_TEMPLATE_RETAILER_ORDER_CREATED, EMAIL_TEMPLATE_RETAILER_ORDER_SHIPPED, EMAIL_TEMPLATE_RETAILER_ORDER_DELIVERED, EMAIL_TEMPLATE_RETAILER_ORDER_REJECTED,

    EMAIL_TEMPLATE_VENDOR_NEW_ORDER, EMAIL_TEMPLATE_VENDOR_ORDER_DELIVERED, EMAIL_TEMPLATE_VENDOR_ORDER_FUNDS_RELEASED,

    EMAIL_TEMPLATE_RETAILER_TICKET_MESSAGE, EMAIL_TEMPLATE_VENDOR_TICKET_MESSAGE,

    EMAIL_TEMPLATE_ADMIN_NEW_ORDER,
    EMAIL_TEMPLATE_ADMIN_NEW_SIGNUP
];

const VENDOR_PAY_METHOD_CHECK = 'check';
const VENDOR_PAY_METHOD_ACH = 'ach';
const VENDOR_PAY_METHOD_WIRE_TRANSFER = 'wire_transfer';
const VENDOR_PAY_METHODS = [VENDOR_PAY_METHOD_CHECK, VENDOR_PAY_METHOD_ACH, VENDOR_PAY_METHOD_WIRE_TRANSFER];

const USER_STATUS_ACTIVE = 'active';
const USER_STATUS_INACTIVE = 'inactive';
const USER_STATUSES = [USER_STATUS_ACTIVE, USER_STATUS_INACTIVE];

const TEAM_USER_STATUS_INVITED = 'invited';
const TEAM_USER_STATUS_ACTIVE = 'active';
const TEAM_USER_STATUS_DELETED = 'deleted';
const TEAM_USER_STATUSES = [TEAM_USER_STATUS_INVITED, TEAM_USER_STATUS_ACTIVE, TEAM_USER_STATUS_DELETED];

const VENDOR_RETURN_PROBABLY_LOW = 'low';
const VENDOR_RETURN_PROBABLY_MEDIUM = 'medium';
const VENDOR_RETURN_PROBABLY_HIGH = 'high';
const VENDOR_RETURN_PROBABLY_VERY_HIGH = 'veryHigh';
const VENDOR_RETURN_PROBABLES = [VENDOR_RETURN_PROBABLY_LOW, VENDOR_RETURN_PROBABLY_MEDIUM, VENDOR_RETURN_PROBABLY_HIGH, VENDOR_RETURN_PROBABLY_VERY_HIGH];

const RETAILER_STATUS_ACTIVE = 'Active';
const RETAILER_STATUS_INACTIVE = 'Inactive';
const RETAILER_STATUSES = [RETAILER_STATUS_ACTIVE, RETAILER_STATUS_INACTIVE];

const APPLICATION_STATUS_DRAFT = 'Draft';
const APPLICATION_STATUS_PENDING = 'Pending';
const APPLICATION_STATUS_DECLINED = 'Declined';
const APPLICATION_STATUS_EXPIRED = 'Expired';
const APPLICATION_STATUS_ACCEPTED = 'Accepted';
const APPLICATION_STATUSES = [APPLICATION_STATUS_DRAFT, APPLICATION_STATUS_PENDING, APPLICATION_STATUS_DECLINED, APPLICATION_STATUS_EXPIRED, APPLICATION_STATUS_ACCEPTED];

const APPLICATION_TYPE_RETAILER = 'retailer';
const APPLICATION_TYPE_VENDOR = 'vendor';
const APPLICATION_TYPES = [APPLICATION_TYPE_RETAILER, APPLICATION_TYPE_VENDOR];


const PRODUCT_STATUS_ACTIVE = 'Active';
const PRODUCT_STATUS_INACTIVE = 'Inactive';
const PRODUCT_STATUS_DELETED = 'Deleted';
const PRODUCT_STATUSES = [PRODUCT_STATUS_ACTIVE, PRODUCT_STATUS_INACTIVE, PRODUCT_STATUS_DELETED];

const PRODUCT_STRAIN_HYBRID = 'hybrid';
const PRODUCT_STRAIN_INDICA = 'indica';
const PRODUCT_STRAIN_SATIVA = 'sativa';
const PRODUCT_STRAINS = [PRODUCT_STRAIN_HYBRID, PRODUCT_STRAIN_INDICA, PRODUCT_STRAIN_SATIVA];

const COUPON_STATUS_ACTIVE = 'Active';
const COUPON_STATUS_INACTIVE = 'Inactive';
const COUPON_STATUS_EXPIRED = 'Expired';
const COUPON_STATUSES = [COUPON_STATUS_ACTIVE, COUPON_STATUS_INACTIVE, COUPON_STATUS_EXPIRED];

const COUPON_TYPE_PERCENT = 'Percent';
const COUPON_TYPE_FIAT = 'Fiat';
const COUPON_TYPES = [COUPON_TYPE_FIAT, COUPON_TYPE_PERCENT];

const RETAILER_ORDER_STATUS_REVIEW = 'in review';
const RETAILER_ORDER_STATUS_APPROVED = 'approved';
const RETAILER_ORDER_STATUS_IN_PROGRESS = 'in progress';
const RETAILER_ORDER_STATUS_COMPLETED = 'completed';
const RETAILER_ORDER_STATUS_CANCELLED = 'cancelled';
const RETAILER_ORDER_STATUS_REJECTED = 'rejected';
const RETAILER_ORDER_STATUSES = [
    RETAILER_ORDER_STATUS_REVIEW,
    RETAILER_ORDER_STATUS_APPROVED,
    RETAILER_ORDER_STATUS_IN_PROGRESS,
    RETAILER_ORDER_STATUS_COMPLETED,
    RETAILER_ORDER_STATUS_CANCELLED,
    RETAILER_ORDER_STATUS_REJECTED
];
const RETAILER_PAYMENT_STATUS_NO_REQUEST = "no request";
const RETAILER_PAYMENT_STATUS_REQUESTED = "requested";
const RETAILER_PAYMENT_STATUS_CONFIRMED = "confirmed";
const RETAILER_PAYMENT_STATUSES = [RETAILER_PAYMENT_STATUS_NO_REQUEST, RETAILER_PAYMENT_STATUS_REQUESTED, RETAILER_PAYMENT_STATUS_CONFIRMED];
const VENDOR_ORDER_STATUS_REVIEW = 'in review';
const VENDOR_ORDER_STATUS_APPROVED = 'approved';
const VENDOR_ORDER_STATUS_IN_PROGRESS = 'in progress';
const VENDOR_ORDER_STATUS_COMPLETED = 'completed';
const VENDOR_ORDER_STATUS_CANCELLED = 'cancelled';
const VENDOR_ORDER_STATUS_REJECTED = 'rejected';
const VENDOR_ORDER_STATUSES = [
    VENDOR_ORDER_STATUS_REVIEW,
    VENDOR_ORDER_STATUS_APPROVED,
    VENDOR_ORDER_STATUS_IN_PROGRESS,
    VENDOR_ORDER_STATUS_COMPLETED,
    VENDOR_ORDER_STATUS_CANCELLED,
    VENDOR_ORDER_STATUS_REJECTED
];

const PRODUCT_ORDER_STATUS_REVIEW = 'in review';
const PRODUCT_ORDER_STATUS_APPROVED = 'approved';
const PRODUCT_ORDER_STATUS_APPROVED_BY_VENDOR = 'approved by vendor';
const PRODUCT_ORDER_STATUS_PREPARE_FOR_SHIPMENT = 'prepare for shipment';
const PRODUCT_ORDER_STATUS_SHIPPED = 'shipped';
const PRODUCT_ORDER_STATUS_DELIVERED = 'delivered';
const PRODUCT_ORDER_STATUS_PAIDOUT = 'paidout';
const PRODUCT_ORDER_STATUS_CANCELLED = 'cancelled';
const PRODUCT_ORDER_STATUS_REJECTED = 'rejected';
const PRODUCT_ORDER_STATUSES = [
    PRODUCT_ORDER_STATUS_REVIEW,
    PRODUCT_ORDER_STATUS_APPROVED,
    PRODUCT_ORDER_STATUS_APPROVED_BY_VENDOR,
    PRODUCT_ORDER_STATUS_PREPARE_FOR_SHIPMENT,
    PRODUCT_ORDER_STATUS_SHIPPED,
    PRODUCT_ORDER_STATUS_DELIVERED,
    PRODUCT_ORDER_STATUS_PAIDOUT,
    PRODUCT_ORDER_STATUS_CANCELLED,
    PRODUCT_ORDER_STATUS_REJECTED
];
const PRODUCT_ORDER_VENDOR_STATUSES = [
    PRODUCT_ORDER_STATUS_APPROVED,
    PRODUCT_ORDER_STATUS_APPROVED_BY_VENDOR,
    PRODUCT_ORDER_STATUS_PREPARE_FOR_SHIPMENT,
    PRODUCT_ORDER_STATUS_SHIPPED,
    PRODUCT_ORDER_STATUS_DELIVERED,
    PRODUCT_ORDER_STATUS_PAIDOUT,
    PRODUCT_ORDER_STATUS_CANCELLED
];

const REVIEW_TYPE_PRODUCT = 'product';
const REVIEW_TYPE_VENDOR = 'vendor';
const REVIEW_TYPES = [REVIEW_TYPE_PRODUCT, REVIEW_TYPE_VENDOR];

const DOCUMENT_TYPE_APPLICATION = 'application document';
const DOCUMENT_TYPE_ORDER = 'order document';
const DOCUMENT_TYPES = [DOCUMENT_TYPE_APPLICATION, DOCUMENT_TYPE_ORDER];

// const ORDER_DOCUMENT_TYPE_RETAILER_PERMIT_DOC = 'retailer permit document';
// const ORDER_DOCUMENT_TYPE_RETAILER_LOI_DOC = 'retailer LOI document';
// const ORDER_DOCUMENT_TYPE_VENDOR_NNF_DOC = 'vendor NNF document';
// const ORDER_DOCUMENT_TYPE_VENDOR_PERMIT_DOC = 'vendor permit document';
// const ORDER_DOCUMENT_TYPE_VENDOR_SHIPPING_DOC = 'vendor shipping document';
// const ORDER_DOCUMENT_TYPE_RETAILER_DELIVERY_DOC = 'retailer delivery document';
// const ORDER_DOCUMENT_TYPES = [
//     ORDER_DOCUMENT_TYPE_RETAILER_PERMIT_DOC,
//     ORDER_DOCUMENT_TYPE_RETAILER_LOI_DOC,
//     ORDER_DOCUMENT_TYPE_VENDOR_NNF_DOC,
//     ORDER_DOCUMENT_TYPE_VENDOR_PERMIT_DOC,
//     ORDER_DOCUMENT_TYPE_VENDOR_SHIPPING_DOC,
//     ORDER_DOCUMENT_TYPE_RETAILER_DELIVERY_DOC
// ];

const ORDER_DOCUMENT_TYPE_FOR_RETAILER = 'document for retailer';
const ORDER_DOCUMENT_TYPE_FOR_VENDOR = 'document for vendor';
const ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR = 'document for retailer and vendor';
const ORDER_DOCUMENT_TYPES = [
    ORDER_DOCUMENT_TYPE_FOR_RETAILER,
    ORDER_DOCUMENT_TYPE_FOR_VENDOR,
    ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR
]

const ORDER_DOCUMENT_STATUS_APPROVED = 'approved';
const ORDER_DOCUMENT_STATUS_NO_UPLOADED = 'no uploaded';
const ORDER_DOCUMENT_STATUS_IN_PROGRESS = 'in progress';
const ORDER_DOCUMENT_STATUS_DECLINED = 'declined';
const ORDER_DOCUMENT_STATUSES = [
    ORDER_DOCUMENT_STATUS_APPROVED,
    ORDER_DOCUMENT_STATUS_NO_UPLOADED,
    ORDER_DOCUMENT_STATUS_IN_PROGRESS,
    ORDER_DOCUMENT_STATUS_DECLINED
];

const TICKET_STATUS_OPENED = 'opened';
const TICKET_STATUS_CLOSED = 'closed';
const TICKET_STATUSES = [TICKET_STATUS_CLOSED, TICKET_STATUS_OPENED];
const TICKET_OPEN_BY_RETAILER = 'retailer';
const TICKET_OPEN_BY_VENDOR = 'vendor';
const TICKET_OPEN_BY_ADMIN = 'admin';
const TICKET_OPEN_BYS = [TICKET_OPEN_BY_RETAILER, TICKET_OPEN_BY_VENDOR, TICKET_OPEN_BY_ADMIN];

const MESSAGE_TYPE_RETAILER = 'retailer';
const MESSAGE_TYPE_VENDOR = 'vendor';
const MESSAGE_TYPE_ADMIN = 'admin';
const MESSAGE_TYPES = [MESSAGE_TYPE_RETAILER, MESSAGE_TYPE_VENDOR, MESSAGE_TYPE_ADMIN];

const PAYMENT_METHOD_ACH = 'ach';
const PAYMENT_METHOD_WIRE_TRANSFER = 'wireTransfer';
const PAYMENT_METHOD_CHECK = 'check';
const PAYMENT_METHOD_CREDIT_CARD = 'creditCard';
const PAYMENT_METHODS = [PAYMENT_METHOD_WIRE_TRANSFER, PAYMENT_METHOD_ACH, PAYMENT_METHOD_CHECK, PAYMENT_METHOD_CREDIT_CARD];

const EMAIL_BLAST_AUDIENCE_TEST = 'test-admins';
const EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_USERS = 'all active users';
const EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_VENDORS = 'all active vendors';
const EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_RETAILERS = 'all active retailers';
const EMAIL_BLAST_AUDIENCE_VENDORS_WITHOUT_TAXID = 'vendors with accepted applications without taxid';
const EMAIL_BLAST_AUDIENCE_VENDORS_WITHOUT_ACCEPTED_APPLICATION = 'vendors without accepted application';
const EMAIL_BLAST_AUDIENCE_RETAILERS_NOT_APPLIED = 'Buyers who haven’t applied';
const EMAIL_BLAST_AUDIENCE_RETAILERS_FINISHED_CREATING_ACCOUNT = 'Buyers who just finished creating an account';
const EMAIL_BLAST_AUDIENCE_VENDORS_APPROVED_APPLICATION_WITH_LOW_SCORES = 'Vendors w/ approved applications with low rating scores based on current algorithm';
const EMAIL_BLAST_AUDIENCE_VENDORS_NOT_APPLIED = 'Vendors, not applied';

const EMAIL_BLAST_AUDIENCES = [
    EMAIL_BLAST_AUDIENCE_TEST,
    EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_USERS,
    EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_VENDORS,
    EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_RETAILERS,
    EMAIL_BLAST_AUDIENCE_VENDORS_WITHOUT_TAXID,
    EMAIL_BLAST_AUDIENCE_VENDORS_WITHOUT_ACCEPTED_APPLICATION,
    EMAIL_BLAST_AUDIENCE_RETAILERS_NOT_APPLIED,
    EMAIL_BLAST_AUDIENCE_RETAILERS_FINISHED_CREATING_ACCOUNT,
    EMAIL_BLAST_AUDIENCE_VENDORS_APPROVED_APPLICATION_WITH_LOW_SCORES,
    EMAIL_BLAST_AUDIENCE_VENDORS_NOT_APPLIED
];

const EMAIL_BLAST_EMAIL_TEMPLATE_BASE = 'eb-base-template';
const EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_APPLICATION_EIN_FIX_SEPTEMBER_2019 = 'eb-vendors-application-ein-fix-september-2019';
const EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_NEW_FEATURES = 'eb-vendors-new-features-template';
const EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_FIX_TAXID = 'eb-vendors-fix-taxid-template';
const EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_FILL_APPLICATION = 'eb-vendors-fill-application-template';
const EMAIL_BLAST_EMAIL_TEMPLATE_RETAILER_CREATED_ACCOUNT = 'eb-retailer-created-account';
const EMAIL_BLAST_EMAIL_TEMPLATE_RETAILER_NOT_APPLIED = 'eb-retailer-not-applied';
const EMAIL_BLAST_EMAIL_TEMPLATE_VENDOR_APPROVED_APPLICATION = 'eb-vendor-approved-application';
const EMAIL_BLAST_EMAIL_TEMPLATE_VENDOR_NOT_APPLIED = 'eb-vendor-not-applied';

const EMAIL_BLAST_EMAIL_TEMPLATES = [
    EMAIL_BLAST_EMAIL_TEMPLATE_BASE,
    EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_APPLICATION_EIN_FIX_SEPTEMBER_2019,
    EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_NEW_FEATURES,
    EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_FIX_TAXID,
    EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_FILL_APPLICATION,
    EMAIL_BLAST_EMAIL_TEMPLATE_RETAILER_CREATED_ACCOUNT,
    EMAIL_BLAST_EMAIL_TEMPLATE_RETAILER_NOT_APPLIED,
    EMAIL_BLAST_EMAIL_TEMPLATE_VENDOR_APPROVED_APPLICATION,
    EMAIL_BLAST_EMAIL_TEMPLATE_VENDOR_NOT_APPLIED
];


const EMAIL_BLAST_STATUS_NEW = 'new';
const EMAIL_BLAST_STATUS_IN_PROGRESS = 'in progress';
const EMAIL_BLAST_STATUS_COMPLETED = 'completed';

const EMAIL_BLAST_STATUSES = [EMAIL_BLAST_STATUS_NEW, EMAIL_BLAST_STATUS_IN_PROGRESS, EMAIL_BLAST_STATUS_COMPLETED];

const EVENT_PRODUCT_CREATED = 'product:created';
const EVENT_PRODUCT_UPDATED = 'product:updated';
const EVENT_PRODUCT_DELETED = 'product:deleted';
const EVENT_COUPON_CREATED = 'coupon:created';
const EVENT_COUPON_UPDATED = 'coupon:updated';
const EVENT_COUPON_EXPIRED = 'coupon:expired';
const EVENT_COUPON_DELETED = 'coupon:deleted';
const EVENT_RETAILER_CREATED = 'retailer:created';
const EVENT_RETAILER_UPDATED = 'retailer:updated';
const EVENT_RETAILER_TERMS_AGREED = 'retailer:terms:agreed';
const EVENT_VENDOR_CREATED = 'vendor:created';
const EVENT_VENDOR_UPDATED = 'vendor:updated';
const EVENT_VENDOR_TERMS_AGREED = 'vendor:terms:agreed';
const EVENT_USER_CREATED = 'user:created';
const EVENT_USER_UPDATED = 'user:updated';

const EVENT_REGULATORYBODY_CREATED = 'regulatory-body:created';
const EVENT_REGULATORYBODY_UPDATED = 'regulatory-body:updated';

const EVENT_REVIEW_CREATED = 'review:created';
const EVENT_REVIEW_UPDATED = 'review:updated';

const EVENT_STATE_CREATED = 'state:created';
const EVENT_STATE_UPDATED = 'state:updated';

const EVENT_DOCUMENT_CREATED = 'document:created';
const EVENT_DOCUMENT_UPDATED = 'document:updated';

const EVENT_TICKET_CREATED = 'ticket:created';
const EVENT_TICKET_UPDATED = 'ticket:updated';
const EVENT_TICKET_ADD_MESSAGE = 'ticket:add-message';
const EVENT_TICKET_CLOSED = 'ticket:closed';
const EVENT_TICKET_REOPENED = 'ticket:reopened';

const EVENT_TEAM_INVITED = 'team:invited';
const EVENT_TEAM_ACCEPTED = 'team:accepted';
const EVENT_TEAM_REMOVED = 'team:removed';

const EVENT_RETAILER_ORDER_CREATED = 'retailer-order:created';
const EVENT_RETAILER_ORDER_UPDATED = 'retailer-order:updated';
const EVENT_RETAILER_ORDER_CHANGE_STATUS = 'retailer-order:status:changed';
const EVENT_VENDOR_ORDER_CREATED = 'vendor-order:created';
const EVENT_VENDOR_ORDER_UPDATED = 'vendor-order:updated';
const EVENT_VENDOR_ORDER_CHANGE_STATUS = 'vendor-order:status:changed';
const EVENT_PRODUCT_ORDER_CREATED = 'product-order:created';
const EVENT_PRODUCT_ORDER_UPDATED = 'product-order:updated';
const EVENT_PRODUCT_ORDER_CHANGE_STATUS = 'product-order:status:changed';
const EVENT_PRODUCT_ORDER_CHANGE_REQUIRED_DOCUMENTS = 'product-order:required-documents:changed';
const EVENT_APPLICATION_CREATED = 'application:created';
const EVENT_APPLICATION_UPDATED = 'application:updated';
const EVENT_APPLICATION_APPROVED = 'application:approved';
const EVENT_APPLICATION_DECLINED = 'application:declined';
const EVENT_APPLICATION_EXPIRED = 'application:expired';

const EVENT_RETAILER_CREDIT_CARD_CREATED = 'retailer:credit-card:created';
const EVENT_RETAILER_CREDIT_CARD_UPDATED = 'retailer:credit-card:updated';
const EVENT_RETAILER_CREDIT_CARD_DELETED = 'retailer:credit-card:deleted';
const EVENT_RETAILER_ADDRESS_CREATED = 'retailer:address:created';
const EVENT_RETAILER_ADDRESS_UPDATED = 'retailer:address:updated';
const EVENT_RETAILER_ADDRESS_DELETED = 'retailer:address:deleted';
const EVENT_VENDOR_ADDRESS_CREATED = 'vendor:address:created';
const EVENT_VENDOR_ADDRESS_UPDATED = 'vendor:address:updated';
const EVENT_VENDOR_ADDRESS_DELETED = 'vendor:address:deleted';

const EVENT_ADMIN_EMAIL_BLAST_TEMPLATE_CREATED = 'admin:emailBlast:template:created';
const EVENT_ADMIN_EMAIL_BLAST_TEMPLATE_UPDATED = 'admin:emailBlast:template:updated';
const EVENT_ADMIN_EMAIL_BLAST_STARTED = 'admin:emailBlast:started';
const EVENT_ADMIN_LOGIN = 'admin:login';
const EVENT_ADMIN_SHADOW_LOGIN = 'admin:shadowLogin';
const EVENT_ADMIN_CREDIT_CARD_DELETED = 'admin:credit-card:deleted';

const EVENT_LOGIN_SUCCESS = 'login:success';
const EVENT_LOGIN_FAILED = 'login:failed';

const LOG_EVENTS = [
    EVENT_ADMIN_EMAIL_BLAST_TEMPLATE_CREATED,
    EVENT_ADMIN_EMAIL_BLAST_STARTED,
    EVENT_ADMIN_LOGIN,
    EVENT_ADMIN_CREDIT_CARD_DELETED,
    EVENT_ADMIN_SHADOW_LOGIN,
    EVENT_PRODUCT_CREATED,
    EVENT_PRODUCT_UPDATED,
    EVENT_PRODUCT_DELETED,
    EVENT_RETAILER_CREATED,
    EVENT_RETAILER_UPDATED,
    EVENT_RETAILER_TERMS_AGREED,
    EVENT_RETAILER_CREDIT_CARD_CREATED,
    EVENT_RETAILER_CREDIT_CARD_UPDATED,
    EVENT_RETAILER_CREDIT_CARD_DELETED,
    EVENT_VENDOR_CREATED,
    EVENT_VENDOR_UPDATED,
    EVENT_VENDOR_TERMS_AGREED,
    EVENT_USER_CREATED,
    EVENT_USER_UPDATED,
    EVENT_REGULATORYBODY_CREATED,
    EVENT_REGULATORYBODY_UPDATED,
    EVENT_REVIEW_CREATED,
    EVENT_REVIEW_UPDATED,
    EVENT_TICKET_CREATED,
    EVENT_TICKET_UPDATED,
    EVENT_TICKET_ADD_MESSAGE,
    EVENT_TICKET_CLOSED,
    EVENT_TICKET_REOPENED,
    EVENT_RETAILER_ORDER_CREATED,
    EVENT_RETAILER_ORDER_UPDATED,
    EVENT_RETAILER_ORDER_CHANGE_STATUS,
    EVENT_VENDOR_ORDER_CREATED,
    EVENT_VENDOR_ORDER_UPDATED,
    EVENT_VENDOR_ORDER_CHANGE_STATUS,
    EVENT_PRODUCT_ORDER_CREATED,
    EVENT_PRODUCT_ORDER_UPDATED,
    EVENT_PRODUCT_ORDER_CHANGE_STATUS,
    EVENT_PRODUCT_ORDER_CHANGE_REQUIRED_DOCUMENTS,
    EVENT_APPLICATION_CREATED,
    EVENT_APPLICATION_UPDATED,
    EVENT_APPLICATION_APPROVED,
    EVENT_APPLICATION_DECLINED,
    EVENT_APPLICATION_EXPIRED,
    EVENT_RETAILER_ADDRESS_CREATED,
    EVENT_RETAILER_ADDRESS_UPDATED,
    EVENT_RETAILER_ADDRESS_DELETED,
    EVENT_VENDOR_ADDRESS_CREATED,
    EVENT_VENDOR_ADDRESS_UPDATED,
    EVENT_VENDOR_ADDRESS_DELETED,
    EVENT_LOGIN_SUCCESS,
    EVENT_LOGIN_FAILED,
    EVENT_TEAM_INVITED,
    EVENT_TEAM_ACCEPTED,
    EVENT_TEAM_REMOVED,
    EVENT_STATE_CREATED,
    EVENT_STATE_UPDATED,
    EVENT_DOCUMENT_CREATED,
    EVENT_DOCUMENT_UPDATED,
    EVENT_COUPON_CREATED,
    EVENT_COUPON_UPDATED,
    EVENT_COUPON_EXPIRED,
    EVENT_COUPON_DELETED
];

const ENV_DEVELOPMENT = 'development';
const ENV_TESTING = 'testing';
const ENV_STAGING = 'staging';
const ENV_PRODUCTION = 'production';

module.exports = {
    ENV: {
        DEVELOPMENT: ENV_DEVELOPMENT,
        TESTING: ENV_TESTING,
        STAGING: ENV_STAGING,
        PRODUCTION: ENV_PRODUCTION
    },
    USER: {
        STATUS: {
            ACTIVE: USER_STATUS_ACTIVE,
            INACTIVE: USER_STATUS_INACTIVE
        },
        STATUSES: USER_STATUSES,
        ROLE: {
            // SHIPPER: USER_ROLE_SHIPPER,
            VENDOR: USER_ROLE_VENDOR,
            RETAILER: USER_ROLE_RETAILER
        },
        ROLES: USER_ROLES
    },
    PRODUCT: {
        TYPE: {
            THC: PRODUCT_TYPE_THC,
            FLOWED_CBD: PRODUCT_TYPE_FLOWED_CBD,
            HEMP_CBD: PRODUCT_TYPE_HEMP_CBD,
            ANCILLARY: PRODUCT_TYPE_ANCILLARY
        },
        TYPES: PRODUCT_TYPES,
        STRAIN: {
            HYBRID: PRODUCT_STRAIN_HYBRID,
            INDICA: PRODUCT_STRAIN_INDICA,
            SATIVA: PRODUCT_STRAIN_SATIVA
        },
        STRAINS: PRODUCT_STRAINS,
        STATUS: {
            ACTIVE: PRODUCT_STATUS_ACTIVE,
            INACTIVE: PRODUCT_STATUS_INACTIVE,
            DELETED: PRODUCT_STATUS_DELETED
        },
        STATUSES: PRODUCT_STATUSES,
        ENVIRONMENT: {
            SUNGROWN: PRODUCT_ENVIRONMENT_SUNGROWN,
            INDOORS: PRODUCT_ENVIRONMENT_INDOORS,
            GREENHOUSE: PRODUCT_ENVIRONMENT_GREENHOUSE,
            N_A: PRODUCT_ENVIRONMENT_N_A
        },
        ENVIRONMENTS: PRODUCT_ENVIRONMENTS
    },
    EMAIL: {
        TYPE: {
            RESTORE_PASSWORD: EMAIL_TYPE_RESTORE_PASSWORD,
            SIGNUP: EMAIL_TYPE_SIGNUP,
            CHANGE_EMAIL: EMAIL_TYPE_CHANGE_EMAIL,
            VERIFY_EMAIL: EMAIL_TYPE_VERIFY_EMAIL,
            TEAMMATE_INVITE: EMAIL_TYPE_TEAMMATE_INVITE,
            VENDOR_APPLICATION_DRAFT: EMAIL_TYPE_VENDOR_APPLICATION_DRAFT,
            VENDOR_APPLICATION_SUBMITTED: EMAIL_TYPE_VENDOR_APPLICATION_SUBMITTED,
            VENDOR_APPLICATION_UPDATED: EMAIL_TYPE_VENDOR_APPLICATION_UPDATED,
            VENDOR_APPLICATION_ACCEPTED: EMAIL_TYPE_VENDOR_APPLICATION_ACCEPTED,
            VENDOR_APPLICATION_DECLINED: EMAIL_TYPE_VENDOR_APPLICATION_DECLINED,
            RETAILER_APPLICATION_DRAFT: EMAIL_TYPE_RETAILER_APPLICATION_DRAFT,
            RETAILER_APPLICATION_SUBMITTED: EMAIL_TYPE_RETAILER_APPLICATION_SUBMITTED,
            RETAILER_APPLICATION_UPDATED: EMAIL_TYPE_RETAILER_APPLICATION_UPDATED,
            RETAILER_APPLICATION_ACCEPTED: EMAIL_TYPE_RETAILER_APPLICATION_ACCEPTED,
            RETAILER_APPLICATION_DECLINED: EMAIL_TYPE_RETAILER_APPLICATION_DECLINED,

            ORDER_DOCUMENT_TYPE_RETAILER_PERMIT_DOC: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_PERMIT_DOC,
            ORDER_DOCUMENT_TYPE_RETAILER_LOI_DOC: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_LOI_DOC,
            ORDER_DOCUMENT_TYPE_VENDOR_NNF_DOC: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_NNF_DOC,
            ORDER_DOCUMENT_TYPE_VENDOR_PERMIT_DOC: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_PERMIT_DOC,
            ORDER_DOCUMENT_TYPE_VENDOR_SHIPPING_DOC: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_VENDOR_SHIPPING_DOC,
            ORDER_DOCUMENT_TYPE_RETAILER_DELIVERY_DOC: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_RETAILER_DELIVERY_DOC,

            ORDER_DOCUMENT_TYPE_FOR_RETAILER: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_RETAILER,
            ORDER_DOCUMENT_TYPE_FOR_VENDOR: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_VENDOR,
            ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR: EMAIL_TYPE_ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR,

            RETAILER_TICKET_MESSAGE: EMAIL_TYPE_RETAILER_TICKET_MESSAGE,
            VENDOR_TICKET_MESSAGE: EMAIL_TYPE_VENDOR_TICKET_MESSAGE,

            RETAILER_ORDER_MISSING_DOCUMENTS: EMAIL_TYPE_RETAILER_ORDER_MISSING_DOCUMENTS,
            RETAILER_ORDER_CREATED: EMAIL_TYPE_RETAILER_ORDER_CREATED,
            RETAILER_ORDER_SHIPPED: EMAIL_TYPE_RETAILER_ORDER_SHIPPED,
            RETAILER_ORDER_DELIVERED: EMAIL_TYPE_RETAILER_ORDER_DELIVERED,
            RETAILER_ORDER_REJECTED: EMAIL_TYPE_RETAILER_ORDER_REJECTED,
            VENDOR_NEW_ORDER: EMAIL_TYPE_VENDOR_NEW_ORDER,
            VENDOR_ORDER_DELIVERED: EMAIL_TYPE_VENDOR_ORDER_DELIVERED,
            VENDOR_ORDER_FUNDS_RELEASED: EMAIL_TYPE_VENDOR_ORDER_FUNDS_RELEASED,
            ADMIN_NEW_ORDER: EMAIL_TYPE_ADMIN_NEW_ORDER,
            ADMIN_NEW_SIGNUP: EMAIL_TYPE_ADMIN_NEW_SIGNUP
        },
        TYPES: EMAIL_TYPES,
        TEMPLATE: {
            RESTORE_PASSWORD: EMAIL_TEMPLATE_RESTORE_PASSWORD,
            SIGNUP: EMAIL_TEMPLATE_SIGNUP,
            CHANGE_EMAIL: EMAIL_TEMPLATE_CHANGE_EMAIL,
            VERIFY_EMAIL: EMAIL_TEMPLATE_VERIFY_EMAIL,
            TEAMMATE_INVITE: EMAIL_TEMPLATE_TEAMMATE_INVITE,
            VENDOR_APPLICATION_SUBMITTED: EMAIL_TEMPLATE_VENDOR_APPLICATION_SUBMITTED,
            VENDOR_APPLICATION_UPDATED: EMAIL_TEMPLATE_VENDOR_APPLICATION_UPDATED,
            VENDOR_APPLICATION_ACCEPTED: EMAIL_TEMPLATE_VENDOR_APPLICATION_ACCEPTED,
            VENDOR_APPLICATION_DECLINED: EMAIL_TEMPLATE_VENDOR_APPLICATION_DECLINED,
            RETAILER_APPLICATION_SUBMITTED: EMAIL_TEMPLATE_RETAILER_APPLICATION_SUBMITTED,
            RETAILER_APPLICATION_UPDATED: EMAIL_TEMPLATE_RETAILER_APPLICATION_UPDATED,
            RETAILER_APPLICATION_ACCEPTED: EMAIL_TEMPLATE_RETAILER_APPLICATION_ACCEPTED,
            RETAILER_APPLICATION_DECLINED: EMAIL_TEMPLATE_RETAILER_APPLICATION_DECLINED,

            RETAILER_TICKET_MESSAGE: EMAIL_TEMPLATE_RETAILER_TICKET_MESSAGE,
            VENDOR_TICKET_MESSAGE: EMAIL_TEMPLATE_VENDOR_TICKET_MESSAGE,

            RETAILER_ORDER_MISSING_DOCUMENTS: EMAIL_TEMPLATE_RETAILER_ORDER_MISSING_DOCUMENTS,
            VENDOR_ORDER_MISSING_DOCUMENTS: EMAIL_TEMPLATE_VENDOR_ORDER_MISSING_DOCUMENTS,
            RETAILER_ORDER_CREATED: EMAIL_TEMPLATE_RETAILER_ORDER_CREATED,
            RETAILER_ORDER_SHIPPED: EMAIL_TEMPLATE_RETAILER_ORDER_SHIPPED,
            RETAILER_ORDER_DELIVERED: EMAIL_TEMPLATE_RETAILER_ORDER_DELIVERED,
            RETAILER_ORDER_REJECTED: EMAIL_TEMPLATE_RETAILER_ORDER_REJECTED,
            VENDOR_NEW_ORDER: EMAIL_TEMPLATE_VENDOR_NEW_ORDER,
            VENDOR_ORDER_FUNDS_RELEASED: EMAIL_TEMPLATE_VENDOR_ORDER_FUNDS_RELEASED,
            ADMIN_NEW_ORDER: EMAIL_TEMPLATE_ADMIN_NEW_ORDER,
            ADMIN_NEW_SIGNUP: EMAIL_TEMPLATE_ADMIN_NEW_SIGNUP
        },
        TEMPLATES: EMAIL_TEMPLATES
    },
    VENDOR: {
        PAY_METHODS: VENDOR_PAY_METHODS,
        PAY_METHOD: {
            CHECK: VENDOR_PAY_METHOD_CHECK,
            ACH: VENDOR_PAY_METHOD_ACH,
            WIRE_TRANSFER: VENDOR_PAY_METHOD_WIRE_TRANSFER
        },
        RETURN_PROBABLY: {
            LOW: VENDOR_RETURN_PROBABLY_LOW,
            MEDIUM: VENDOR_RETURN_PROBABLY_MEDIUM,
            HIGH: VENDOR_RETURN_PROBABLY_HIGH,
            VERY_HIGH: VENDOR_RETURN_PROBABLY_VERY_HIGH
        },
        RETURN_PROBABLES: VENDOR_RETURN_PROBABLES
    },
    TEAM: {
        USER_STATUS: {
            INVITED: TEAM_USER_STATUS_INVITED,
            ACTIVE: TEAM_USER_STATUS_ACTIVE,
            DELETED: TEAM_USER_STATUS_DELETED
        },
        USER_STATUSES: TEAM_USER_STATUSES
    },
    RETAILER_PAYMENT: {
        STATUS: {
            NO_REQUEST: RETAILER_PAYMENT_STATUS_NO_REQUEST,
            REQUESTED: RETAILER_PAYMENT_STATUS_REQUESTED,
            CONFIRMED: RETAILER_PAYMENT_STATUS_CONFIRMED
        },
        SATUSES: RETAILER_PAYMENT_STATUSES
    },
    APPLICATION: {
        TYPE: {
            RETAILER: APPLICATION_TYPE_RETAILER,
            VENDOR: APPLICATION_TYPE_VENDOR
        },
        TYPES: APPLICATION_TYPES,
        STATUSES: APPLICATION_STATUSES,
        STATUS: {
            DRAFT: APPLICATION_STATUS_DRAFT,
            PENDING: APPLICATION_STATUS_PENDING,
            DECLINED: APPLICATION_STATUS_DECLINED,
            EXPIRED: APPLICATION_STATUS_EXPIRED,
            ACCEPTED: APPLICATION_STATUS_ACCEPTED
        }
    },
    RETAILER_ORDER: {
        STATUS: {
            REVIEW: RETAILER_ORDER_STATUS_REVIEW,
            APPROVED: RETAILER_ORDER_STATUS_APPROVED,
            IN_PROGRESS: RETAILER_ORDER_STATUS_IN_PROGRESS,
            COMPLETED: RETAILER_ORDER_STATUS_COMPLETED,
            CANCELLED: RETAILER_ORDER_STATUS_CANCELLED,
            REJECTED: RETAILER_ORDER_STATUS_REJECTED
        },
        STATUSES: RETAILER_ORDER_STATUSES
    },
    VENDOR_ORDER: {
        STATUS: {
            REVIEW: VENDOR_ORDER_STATUS_REVIEW,
            APPROVED: VENDOR_ORDER_STATUS_APPROVED,
            IN_PROGRESS: VENDOR_ORDER_STATUS_IN_PROGRESS,
            COMPLETED: VENDOR_ORDER_STATUS_COMPLETED,
            CANCELLED: VENDOR_ORDER_STATUS_CANCELLED,
            REJECTED: VENDOR_ORDER_STATUS_REJECTED
        },
        STATUSES: VENDOR_ORDER_STATUSES
    },
    PRODUCT_ORDER: {
        STATUS: {
            REVIEW: PRODUCT_ORDER_STATUS_REVIEW,
            APPROVED: PRODUCT_ORDER_STATUS_APPROVED,
            APPROVED_BY_VENDOR: PRODUCT_ORDER_STATUS_APPROVED_BY_VENDOR,
            PREPARE_FOR_SHIPMENT: PRODUCT_ORDER_STATUS_PREPARE_FOR_SHIPMENT,
            SHIPPED: PRODUCT_ORDER_STATUS_SHIPPED,
            DELIVERED: PRODUCT_ORDER_STATUS_DELIVERED,
            PAIDOUT: PRODUCT_ORDER_STATUS_PAIDOUT,
            CANCELLED: PRODUCT_ORDER_STATUS_CANCELLED,
            REJECTED: PRODUCT_ORDER_STATUS_REJECTED
        },
        VENDOR_STATUSES: PRODUCT_ORDER_VENDOR_STATUSES,
        STATUSES: PRODUCT_ORDER_STATUSES
    },
    REVIEW: {
        TYPE: {
            PRODUCT: REVIEW_TYPE_PRODUCT,
            VENDOR: REVIEW_TYPE_VENDOR
        },
        TYPES: REVIEW_TYPES
    },
    COUPON: {
        STATUS: {
            ACTIVE: COUPON_STATUS_ACTIVE,
            INACTIVE: COUPON_STATUS_INACTIVE,
            EXPIRED: COUPON_STATUS_EXPIRED
        },
        STATUSES: COUPON_STATUSES,
        TYPE: {
            PERCENT: COUPON_TYPE_PERCENT,
            FIAT: COUPON_TYPE_FIAT
        },
        TYPES: COUPON_TYPES
    },
    DOCUMENT: {
        TYPE: {
            APPLICATION: DOCUMENT_TYPE_APPLICATION,
            ORDER: DOCUMENT_TYPE_ORDER
        },
        TYPES: DOCUMENT_TYPES
    },
    ORDER_DOCUMENT: {
        STATUS: {
            APPROVED: ORDER_DOCUMENT_STATUS_APPROVED,
            NO_UPLOADED: ORDER_DOCUMENT_STATUS_NO_UPLOADED,
            IN_PROGRESS: ORDER_DOCUMENT_STATUS_IN_PROGRESS,
            DECLINED: ORDER_DOCUMENT_STATUS_DECLINED
        },
        STATUSES: ORDER_DOCUMENT_STATUSES,
        TYPE: {
            ORDER_DOCUMENT_TYPE_FOR_RETAILER: ORDER_DOCUMENT_TYPE_FOR_RETAILER, // 1
            ORDER_DOCUMENT_TYPE_FOR_VENDOR: ORDER_DOCUMENT_TYPE_FOR_VENDOR, // 2
            ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR: ORDER_DOCUMENT_TYPE_FOR_RETAILER_AND_VENDOR, // 3
        },
        TYPES: ORDER_DOCUMENT_TYPES
    },
    TICKET: {
        STATUS: {
            OPENED: TICKET_STATUS_OPENED,
            CLOSED: TICKET_STATUS_CLOSED
        },
        STATUSES: TICKET_STATUSES,
        OPEN_BY: {
            RETAILER: TICKET_OPEN_BY_RETAILER,
            VENDOR: TICKET_OPEN_BY_VENDOR,
            ADMIN: TICKET_OPEN_BY_ADMIN
        },
        OPEN_BYS: TICKET_OPEN_BYS,
        MESSAGE: {
            TYPE: {
                RETAILER: MESSAGE_TYPE_RETAILER,
                VENDOR: MESSAGE_TYPE_VENDOR,
                ADMIN: MESSAGE_TYPE_ADMIN
            },
            TYPES: MESSAGE_TYPES
        }
    },
    PAYMENT: {
        METHOD: {
            ACH: PAYMENT_METHOD_ACH,
            WIRE_TRANSFER: PAYMENT_METHOD_WIRE_TRANSFER,
            CHECK: PAYMENT_METHOD_CHECK,
            CREDIT_CARD: PAYMENT_METHOD_CREDIT_CARD
        },
        METHODS: PAYMENT_METHODS
    },
    EMAIL_BLAST: {
        STATUS: {
            NEW: EMAIL_BLAST_STATUS_NEW,
            IN_PROGRESS: EMAIL_BLAST_STATUS_IN_PROGRESS,
            COMPLETE: EMAIL_BLAST_STATUS_COMPLETED
        },
        STATUSES: EMAIL_BLAST_STATUSES,
        AUDIENCES: EMAIL_BLAST_AUDIENCES,
        AUDIENCE: {
            TEST: EMAIL_BLAST_AUDIENCE_TEST,
            ALL_ACTIVE_USERS: EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_USERS,
            ALL_ACTIVE_VENDORS: EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_VENDORS,
            ALL_ACTIVE_RETAILERS: EMAIL_BLAST_AUDIENCE_ALL_ACTIVE_RETAILERS,
            VENDORS_WITHOUT_TAXID: EMAIL_BLAST_AUDIENCE_VENDORS_WITHOUT_TAXID,
            VENDORS_WITHOUT_ACCEPTED_APPLICATION: EMAIL_BLAST_AUDIENCE_VENDORS_WITHOUT_ACCEPTED_APPLICATION,
            RETAILERS_NOT_APPLIED: EMAIL_BLAST_AUDIENCE_RETAILERS_NOT_APPLIED,
            RETAILERS_FINISHED_CREATING_ACCOUNT: EMAIL_BLAST_AUDIENCE_RETAILERS_FINISHED_CREATING_ACCOUNT,
            VENDORS_APPROVED_APPLICATION_WITH_LOW_SCORES: EMAIL_BLAST_AUDIENCE_VENDORS_APPROVED_APPLICATION_WITH_LOW_SCORES,
            VENDORS_NOT_APPLIED: EMAIL_BLAST_AUDIENCE_VENDORS_NOT_APPLIED
        },
        EMAIL_TEMPLATES: EMAIL_BLAST_EMAIL_TEMPLATES,
        EMAIL_TEMPLATE: {
            BASE: EMAIL_BLAST_EMAIL_TEMPLATE_BASE,
            VENDORS_APPLICATION_EIN_FIX_SEPTEMBER_2019: EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_APPLICATION_EIN_FIX_SEPTEMBER_2019,
            VENDORS_NEW_FEATURES: EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_NEW_FEATURES,
            VENDORS_FIX_TAXID: EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_FIX_TAXID,
            VENDORS_FILL_APPLICATION: EMAIL_BLAST_EMAIL_TEMPLATE_VENDORS_FILL_APPLICATION,
            RETAILER_CREATED_ACCOUNT: EMAIL_BLAST_EMAIL_TEMPLATE_RETAILER_CREATED_ACCOUNT,
            RETAILER_NOT_APPLIED: EMAIL_BLAST_EMAIL_TEMPLATE_RETAILER_NOT_APPLIED,
            VENDOR_APPROVED_APPLICATION: EMAIL_BLAST_EMAIL_TEMPLATE_VENDOR_APPROVED_APPLICATION,
            VENDOR_NOT_APPLIED: EMAIL_BLAST_EMAIL_TEMPLATE_VENDOR_NOT_APPLIED
        }
    },
    LOG: {
        EVENTS: LOG_EVENTS,
        PRODUCT: {
            CREATED: EVENT_PRODUCT_CREATED,
            UPDATED: EVENT_PRODUCT_UPDATED,
            DELETED: EVENT_PRODUCT_DELETED
        },
        COUPON: {
            CREATED: EVENT_COUPON_CREATED,
            UPDATED: EVENT_COUPON_UPDATED,
            EXPIRED: EVENT_COUPON_EXPIRED,
            DELETED: EVENT_COUPON_DELETED,
        },
        RETAILER: {
            CREATED: EVENT_RETAILER_CREATED,
            UPDATED: EVENT_RETAILER_UPDATED,
            ADDRESS: {
                CREATED: EVENT_RETAILER_ADDRESS_CREATED,
                UPDATED: EVENT_RETAILER_ADDRESS_UPDATED,
                DELETED: EVENT_RETAILER_ADDRESS_DELETED
            },
            CREDIT_CARD: {
                CREATED: EVENT_RETAILER_CREDIT_CARD_CREATED,
                UPDATED: EVENT_RETAILER_CREDIT_CARD_UPDATED,
                DELETED: EVENT_RETAILER_CREDIT_CARD_DELETED
            },
            TERMS_AGREED: EVENT_RETAILER_TERMS_AGREED
        },
        VENDOR: {
            CREATED: EVENT_VENDOR_CREATED,
            UPDATED: EVENT_VENDOR_UPDATED,
            ADDRESS: {
                CREATED: EVENT_VENDOR_ADDRESS_CREATED,
                UPDATED: EVENT_VENDOR_ADDRESS_UPDATED,
                DELETED: EVENT_VENDOR_ADDRESS_DELETED
            },
            TERMS_AGREED: EVENT_VENDOR_TERMS_AGREED
        },
        ADMIN: {
            EMAIL_BLAST: {
                TEMPLATE: {
                    CREATED: EVENT_ADMIN_EMAIL_BLAST_TEMPLATE_CREATED,
                    UPDATED: EVENT_ADMIN_EMAIL_BLAST_TEMPLATE_UPDATED
                },
                STARTED: EVENT_ADMIN_EMAIL_BLAST_STARTED
            },
            CREDIT_CARD: {
                DELETED: EVENT_ADMIN_CREDIT_CARD_DELETED
            },
            LOGIN: EVENT_ADMIN_LOGIN,
            SHADOW_LOGIN: EVENT_ADMIN_SHADOW_LOGIN
        },
        USER: {
            CREATED: EVENT_USER_CREATED,
            UPDATED: EVENT_USER_UPDATED,
            LOGIN_SUCCESS: EVENT_LOGIN_SUCCESS,
            LOGIN_FAILED: EVENT_LOGIN_FAILED
        },
        REGULATORYBODY: {
            CREATED: EVENT_REGULATORYBODY_CREATED,
            UPDATED: EVENT_REGULATORYBODY_UPDATED
        },
        RETAILER_ORDER: {
            CREATED: EVENT_RETAILER_ORDER_CREATED,
            UPDATED: EVENT_RETAILER_ORDER_UPDATED,
            CHANGE_STATUS: EVENT_RETAILER_ORDER_CHANGE_STATUS
        },
        VENDOR_ORDER: {
            CREATED: EVENT_VENDOR_ORDER_CREATED,
            UPDATED: EVENT_VENDOR_ORDER_UPDATED,
            CHANGE_STATUS: EVENT_VENDOR_ORDER_CHANGE_STATUS
        },
        PRODUCT_ORDER: {
            CREATED: EVENT_PRODUCT_ORDER_CREATED,
            UPDATED: EVENT_PRODUCT_ORDER_UPDATED,
            CHANGE_STATUS: EVENT_PRODUCT_ORDER_CHANGE_STATUS,
            CHANGE_REQUIRED_DOCUMENTS: EVENT_PRODUCT_ORDER_CHANGE_REQUIRED_DOCUMENTS
        },
        REVIEW: {
            CREATED: EVENT_REVIEW_CREATED,
            UPDATED: EVENT_REVIEW_UPDATED
        },
        STATES: {
            CREATED: EVENT_STATE_CREATED,
            UPDATED: EVENT_STATE_UPDATED
        },
        TEAM: {
            INVITED: EVENT_TEAM_INVITED,
            ACCEPTED: EVENT_TEAM_ACCEPTED,
            REMOVED: EVENT_TEAM_REMOVED
        },
        TICKET: {
            CREATED: EVENT_TICKET_CREATED,
            UPDATED: EVENT_TICKET_UPDATED,
            ADD_MESSAGE: EVENT_TICKET_ADD_MESSAGE,
            CLOSED: EVENT_TICKET_CLOSED,
            REOPENED: EVENT_TICKET_REOPENED
        },
        APPLICATION: {
            CREATED: EVENT_APPLICATION_CREATED,
            UPDATED: EVENT_APPLICATION_UPDATED,
            APPROVED: EVENT_APPLICATION_APPROVED,
            EXPIRED: EVENT_APPLICATION_EXPIRED,
            DECLINED: EVENT_APPLICATION_DECLINED
        },
        DOCUMENTS: {
            CREATED: EVENT_DOCUMENT_CREATED,
            UPDATED: EVENT_DOCUMENT_UPDATED
        },
    },
    einRegex: /^\d{9}$/gm
};
