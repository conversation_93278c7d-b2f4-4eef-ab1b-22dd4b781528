'use strict';

const mongoose = require('mongoose');
const { preSaveGenerateUniqueField } = require('../services/util');
const passwordGenerator = require('password-generator');
const { ObjectId } = mongoose.Schema.Types;

const BrokerLicenseSchema = new mongoose.Schema(
    {
        key: {
            type: String,
            systemLogLabel: 'Key',
            index: true
        },
        _vendor: {
            type: ObjectId,
            ref: 'Vendor',
            required: true,
            index: true
        },
        licenseNumber: {
            type: String,
            systemLogLabel: 'License Number',
            required: true,
            index: true
        },
        licenseHolder: {
            type: String,
            systemLogLabel: 'License Holder Name',
            required: true
        },
        businessName: {
            type: String,
            systemLogLabel: 'License Business Name'
        },
        licenseType: {
            type: String,
            systemLogLabel: 'License Type'
        },
        _state: {
            type: ObjectId,
            ref: 'State',
            systemLogLabel: 'License State',
            index: true
        },
        county: {
            type: String,
            systemLogLabel: 'License County'
        },
        issuedDate: {
            type: Date,
            systemLogLabel: 'License Issued Date'
        },
        expirationDate: {
            type: Date,
            systemLogLabel: 'License Expiration Date',
            required: true,
            index: true
        },
        status: {
            type: String,
            enum: ['active', 'expired', 'suspended', 'revoked'],
            default: 'active',
            systemLogLabel: 'License Status',
            index: true
        },
        notes: {
            type: String,
            systemLogLabel: 'License Notes'
        },
        deletedAt: {
            type: Date,
            index: true
        }
    },
    { timestamps: true }
);

// Compound index for vendor + license number uniqueness
BrokerLicenseSchema.index({ _vendor: 1, licenseNumber: 1 }, { unique: true });

// Index for finding active licenses
BrokerLicenseSchema.index({ _vendor: 1, status: 1, deletedAt: 1 });

BrokerLicenseSchema
    .static('getForVendor', getForVendor)
    .static('createForVendor', createForVendor)
    .method('formatPublic', formatPublic)
    .method('formatAdmin', formatAdmin)
    .method('isExpired', isExpired)
    .method('isActive', isActive)
    .pre('save', preSaveGenerateUniqueField('key', generateKey))
    .pre('save', preSaveUpdateStatus);

const BrokerLicense = mongoose.model('BrokerLicense', BrokerLicenseSchema);

function generateKey() {
    return passwordGenerator(8, false, /[0-9A-HJ-NP-Z]/, 'BL');
}

async function getForVendor(vendor) {
    const licenses = await BrokerLicense.find({ 
        _vendor: vendor._id,
        deletedAt: { $exists: false }
    }).populate('_state').sort({ createdAt: -1 });
    return licenses;
}

async function createForVendor(data) {
    const license = new BrokerLicense({
        ...data,
        _vendor: data.vendor._id
    });
    await license.save();
    return license;
}

function formatPublic() {
    return {
        key: this.key,
        licenseNumber: this.licenseNumber,
        licenseHolder: this.licenseHolder,
        businessName: this.businessName,
        licenseType: this.licenseType,
        state: this._state,
        county: this.county,
        issuedDate: this.issuedDate,
        expirationDate: this.expirationDate,
        status: this.status,
        isExpired: this.isExpired(),
        isActive: this.isActive(),
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
    };
}

function formatAdmin() {
    return {
        ...this.formatPublic(),
        _id: this._id,
        _vendor: this._vendor,
        notes: this.notes,
        deletedAt: this.deletedAt
    };
}

function isExpired() {
    return new Date() > this.expirationDate;
}

function isActive() {
    return this.status === 'active' && !this.isExpired() && !this.deletedAt;
}

function preSaveUpdateStatus() {
    // Auto-update status based on expiration date
    if (this.isExpired() && this.status === 'active') {
        this.status = 'expired';
    }
}

module.exports = BrokerLicense;
