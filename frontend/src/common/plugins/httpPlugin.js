import * as axios from 'axios';

export default {
    install(Vue, options = {}) {
        if (options.host) {
            axios.defaults.baseURL = options.host;
        }
        axios.defaults.headers.common.Accept = 'application/json';
        axios.defaults.headers.common['Content-Type'] = 'application/json';

        axios.interceptors.response.use(function (response) {
            let body = response.data;
            if (!body.data) {
                return body;
            }
            let data = body.data;

            if (body.pagination) {
                data.pagination = body.pagination;
            }
            if (body.filters) {
                data.filters = body.filters;
            }
            if (body.statistics) {
                data.statistics = body.statistics;
            }
            if (body.meta && options.store) {
                options.store.commit('updateMeta', body.meta);
            }
            return data;
        }, function (error) {
            if (axios.isCancel(error)) {
                return Promise.reject(error);
            }
            if (error.response.status === 401 || error.response.status === 403) {
                window.location.href = '/login';
            }
            let body = error.response.data;
            let errors;
            if (body.errors) {
                errors = body.errors;
                errors._error = body.message || 'An error occurred. Try to repeat in a minute.';
            }
            else {
                errors = {
                    _error: body.message || 'An error occurred. Try to repeat in a minute.'
                };
            }
            // eslint-disable-next-line
            window.heap && (window.heap.track instanceof Function) && window.heap.track('Backend Error', {
                status: error.response.status,
                message: errors._error
            });
            return Promise.reject(errors);
        });
        axios.defaults.paramsSerializer = function (params) {
            return Object.entries(params).map(([key, value]) => {
                if (Array.isArray(value)) {
                    return value.map(val => `${encodeURIComponent(key)}=${encodeURIComponent(val)}`).join('&');
                }
                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
            })
                .join('&');
        };

        // Add axios to Vue prototype so it can be accessed as this.$http
        Vue.prototype.$http = axios;
    }
};
