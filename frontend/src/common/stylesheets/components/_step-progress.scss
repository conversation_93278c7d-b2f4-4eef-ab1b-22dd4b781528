/* VARIABLES */
/* COLORS */
$brand-primary: #1aa79e;
$brand-secondary: #dedc00;
$brand-lemon: #fff219;
$brand-quaternary: #f282a5;
$brand-menthol: #14877d;
$brand-coral: rgb(250, 90, 85);
$brand-paprika: rgb(205, 0, 125);
$color-white: #fff;
$color-dark: #676767;
$color-gray: #cecece;
$color-lightgray: #ededed;
$color-jungle: #193805;

/* FONT */
$font-montserrat: "Montserrat", sans-serif;
$font-weight-bold: 700;


.register {
    display: block;
    color: $color-white;
    max-width: 540px;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 4rem;
    background: $color-jungle;
    background: linear-gradient(145deg, #173205, #1b3c05);
    box-shadow: 38px 38px 77px #132a04,
        -38px -38px 77px #1f4606;

    &-icon {
        display: flex;
        background: $color-white;
        border-radius: 2rem;
        width: 50px;
        height: 50px;
        padding: 1rem;
        margin: -50px auto 20px;

        &-item {
            width: 100%;
        }
    }

    &-title {
        font-weight: 300;
        font-size: 1.5rem;
        text-transform: uppercase;
        letter-spacing: 0.2rem;
        text-align: center;
        color: $color-white;
        padding: 0 2rem;
        margin-top: 2rem;
    }

    &-stepper {
        display: flex;
        justify-content: space-between;
        width: 100%;
        position: relative;
        margin: 0 auto 1.5em;

        &::before {
            z-index: 0;
            content: "";
            display: block;
            position: absolute;
            height: 2px;
            top: calc(50% - 1px);
            background: $color-gray;
            width: calc(100% - 20px);
        }

        .step {
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            border: 2px solid $color-gray;
            color: $color-gray;
            background-color: $color-white;
            border-radius: 50%;
            min-width: 25px;
            min-height: 25px;
            line-height: 20px;
            font-size: 16px;
            cursor: pointer;

            &-active {
                color: $brand-primary;
                background-color: $color-white;
                border-color: $brand-primary;
            }

            &-done {
                color: #8dc640;
                border-color: #8dc640;
            }

            &-invalid {
                color: #dc3545;
                border-color: #dc3545;
                background-color: #fff5f5;
            }

            &-disabled {
                color: #ccc;
                border-color: #ccc;
                cursor: not-allowed;
                opacity: 0.6;
            }

            &-number {
                font-family: $font-montserrat;
                font-weight: 800;
                line-height: 1;
                vertical-align: middle;
            }
        }
    }

    .form {
        &-group {
            display: flex;
            flex-flow: row;
            justify-content: flex-start;
            align-items: baseline;

            label {
                text-align: left;
                font-size: 1.1rem;
                line-height: 1.1;
                padding-bottom: 0.5rem;
            }

            &.cta-step {
                color: $color-white;
                justify-content: space-between;

                .cta.prev {
                    padding: 10px 30px;
                }
            }

            &.new-password {
                margin-top: 2rem;
            }
        }

        .cta-color,
        .cta-color input,
        .cta-color .link_text {
            color: $color-white;
            font-family: $font-montserrat;
            font-size: 1.1rem;
            text-decoration: none;
        }

        .cta-color .link_wrap {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;

            .arrow-prev {
                position: relative;
                display: inline-block;
                transform: translate(0);
                transition: transform .3s ease-in-out;

                &::before {
                    content: '<';
                    position: absolute;
                    top: -17px;
                    left: -25px;
                }
            }

            .arrow-next {
                position: relative;
                display: inline-block;
                transform: translate(0);
                transition: transform .3s ease-in-out;

                &::before {
                    content: '>';
                    position: absolute;
                    top: -10px;
                    left: -25px;
                }
            }

            &:hover .arrow-prev {
                transform: translate(-5px);
            }

            &:hover .arrow-next {
                transform: translate(5px);
            }
        }
    }

    // Override styles for input
    input[type="submit"],
    input[type="text"],
    input[type="tel"],
    input[type="email"],
    input[type="date"] {
        -webkit-appearance: none;
        border: 0;
        border-radius: 5px;
        padding: 1.3rem 1rem;
        width: 100%;
        margin: 0.5rem;
    }

    input[type="submit"] {
        cursor: pointer;
        position: relative;
        padding-right: 36px;
        background: none;
        width: fit-content;

        &:hover,
        &:focus {
            box-shadow: unset;
            transform: none;
        }

        &::after {
            content: "";
            display: block;
            position: absolute;
            right: 0;
            top: 50%;
            border-radius: 50px;
            border: 1px solid $brand-primary;
            height: 25px;
            width: 25px;
            margin-top: -14px;
            pointer-events: none;
            transition: all 0.33s cubic-bezier(0.12, 0.75, 0.4, 1);
        }
    }

    &-btn input {
        color: $color-white;
        font-size: 1.2rem;
        font-family: $font-montserrat;
        font-weight: 800;
        line-height: 1;
        width: fit-content;
        background: linear-gradient(145deg, #1b3c05, #173205);
        box-shadow: 20px 20px 60px #142c04,
            -20px -20px 60px #1f4406;

        &:hover {
            background: linear-gradient(145deg, #173205, #1b3c05);
            box-shadow: 20px 20px 60px #142c04,
                -20px -20px 60px #1f4406;
        }
    }

    // Transition SLIDE FADE
    .slide-fade-enter-active {
        transition: all .3s ease;
    }

    .slide-fade-leave-active {
        display: none;
        transition: all .4s cubic-bezier(1.0, 0.5, 0.8, 1.0);
    }

    .slide-fade-enter,
    .slide-fade-leave-to {
        transform: translateX(10px);
        opacity: 0;
    }
}