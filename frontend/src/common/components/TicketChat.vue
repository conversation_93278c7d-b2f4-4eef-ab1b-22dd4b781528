<template>
    <div class="border-radius-3">
        <div class="bg-gray-2 px-6 py-3 border-top-radius-3">
            <div class="text-size-18 text-gray-dark text-500 text-wrap">
                <template v-if="ticket.retailer">
                    Chat With {{ticket.vendor.storeName}}
                </template>
                <template v-else>
                    Support Ticket
                </template>
            </div>
        </div>
        <div class="bg-primary px-6 py-3 text-white text-500">
            {{ticket.subject}}
        </div>
        <div class="bg-white px-6 py-3">
            <ticket-message
                    v-for="(message, index) in ticket.messages"
                    :key="index"
                    :retailer="ticket.retailer"
                    :vendor="ticket.vendor"
                    :showFor="showFor"
                    :message="message"/>
        </div>
        <hr class="my-0 hr-gray-7">
        <div class="bg-gray-2 p-6 border-bottom-radius-3">
            <div class="form-group">
                <label class="control-label">
                    Write your message
                </label>
                <textarea class="form-control"
                          cols="30"
                          rows="3"
                          v-model="text"
                          v-validate="'required'"
                          data-vv-name="text"></textarea>
            </div>
            <div class="text-right">
                <button class="btn btn-info btn-width-xs btn-xs-block" @click="send" :disabled="!text">Send</button>
            </div>
        </div>

    </div>
</template>

<script>
import TicketMessage from './TicketMessage';

export default {
    name: 'TicketChat',
    components: {TicketMessage},
    props: {
        ticket: Object,
        showFor: String
    },
    data() {
        return {
            text: ''
        };
    },
    computed: {

    },
    methods: {
        send() {
            this.$validator.validateAll()
                .then(result => {
                    if (!result) {
                        return;
                    }
                    this.$emit('send', this.text);
                    this.text = '';
                });
        }
    }
};
</script>
