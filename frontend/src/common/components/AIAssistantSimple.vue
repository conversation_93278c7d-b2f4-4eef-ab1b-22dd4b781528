<template>
    <div class="ai-assistant-simple">
        <!-- Debug Element -->
        <div style="position: fixed; bottom: 90px; right: 20px; background: blue; color: white; padding: 10px; border-radius: 5px; z-index: 10000; font-size: 12px;">
            AI Component Loaded
        </div>

        <!-- Toggle Button -->
        <button
            class="ai-assistant-btn"
            @click="toggleChat"
            :class="{ 'active': isOpen }"
            title="AI Assistant">
            🤖
        </button>

        <!-- Chat Interface -->
        <div v-if="isOpen" class="ai-assistant-chat">
            <div class="chat-header">
                <span>🤖 AI Assistant</span>
                <button @click="closeChat" class="close-btn">×</button>
            </div>
            
            <div class="chat-body">
                <div class="welcome-message">
                    <p>👋 Hi! I'm your AI assistant. I can help you with:</p>
                    <ul>
                        <li>Navigating the platform</li>
                        <li>Managing products</li>
                        <li>Account settings</li>
                        <li>General questions</li>
                    </ul>
                </div>
                
                <div v-if="messages.length > 0" class="messages">
                    <div 
                        v-for="(message, index) in messages" 
                        :key="index" 
                        class="message"
                        :class="message.type">
                        <div class="message-content">{{ message.content }}</div>
                    </div>
                </div>
                
                <div v-if="isLoading" class="loading">
                    <span>AI is thinking...</span>
                </div>
            </div>
            
            <div class="chat-input">
                <input 
                    v-model="currentMessage" 
                    @keyup.enter="sendMessage"
                    placeholder="Ask me anything..."
                    class="message-input"
                    :disabled="isLoading">
                <button 
                    @click="sendMessage" 
                    :disabled="!currentMessage.trim() || isLoading"
                    class="send-btn">
                    Send
                </button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AIAssistantSimple',
    data() {
        return {
            isOpen: false,
            messages: [],
            currentMessage: '',
            isLoading: false
        };
    },
    mounted() {
        console.log('🤖 AIAssistantSimple mounted successfully!');
    },
    methods: {
        toggleChat() {
            this.isOpen = !this.isOpen;
            console.log('AI Assistant toggled:', this.isOpen);
        },
        
        closeChat() {
            this.isOpen = false;
        },
        
        async sendMessage() {
            if (!this.currentMessage.trim() || this.isLoading) return;
            
            const userMessage = this.currentMessage.trim();
            this.messages.push({
                type: 'user',
                content: userMessage,
                timestamp: new Date()
            });
            
            this.currentMessage = '';
            this.isLoading = true;
            
            try {
                const response = await this.$http.post('/ai-assistant/chat', {
                    message: userMessage,
                    context: {
                        currentPage: this.$route.path,
                        userRole: 'vendor'
                    }
                });
                
                this.messages.push({
                    type: 'ai',
                    content: response.data.response || 'I received your message!',
                    timestamp: new Date()
                });
            } catch (error) {
                console.error('AI Assistant error:', error);
                this.messages.push({
                    type: 'error',
                    content: 'Sorry, I encountered an error. Please try again.',
                    timestamp: new Date()
                });
            } finally {
                this.isLoading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.ai-assistant-simple {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-assistant-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    cursor: pointer;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 20px rgba(23, 162, 184, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(23, 162, 184, 0.4);
    }
    
    &.active {
        background: #dc3545;
    }
}

.ai-assistant-chat {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 450px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-body {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.welcome-message {
    margin-bottom: 16px;
    
    p {
        margin: 0 0 8px 0;
        font-weight: 500;
    }
    
    ul {
        margin: 0;
        padding-left: 20px;
        font-size: 14px;
        color: #666;
    }
}

.messages {
    margin-top: 16px;
}

.message {
    margin-bottom: 12px;
    
    &.user {
        text-align: right;
        
        .message-content {
            background: #17a2b8;
            color: white;
            display: inline-block;
            padding: 8px 12px;
            border-radius: 18px 18px 4px 18px;
            max-width: 80%;
        }
    }
    
    &.ai {
        text-align: left;
        
        .message-content {
            background: #f1f3f4;
            color: #333;
            display: inline-block;
            padding: 8px 12px;
            border-radius: 18px 18px 18px 4px;
            max-width: 80%;
        }
    }
    
    &.error {
        text-align: left;
        
        .message-content {
            background: #f8d7da;
            color: #721c24;
            display: inline-block;
            padding: 8px 12px;
            border-radius: 18px 18px 18px 4px;
            max-width: 80%;
        }
    }
}

.loading {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 8px;
}

.chat-input {
    padding: 16px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 8px;
}

.message-input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 8px 16px;
    outline: none;
    font-size: 14px;
    
    &:focus {
        border-color: #17a2b8;
    }
    
    &:disabled {
        background: #f8f9fa;
        cursor: not-allowed;
    }
}

.send-btn {
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
    
    &:hover:not(:disabled) {
        background: #138496;
    }
    
    &:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
}

@media (max-width: 480px) {
    .ai-assistant-chat {
        width: calc(100vw - 40px);
        height: 60vh;
        bottom: 70px;
        right: -10px;
    }
}
</style>
