<template>
    <div class="col-md-2">

        <div class="clearfix">
            <div class="list-group-horizontal-sm">
                <div class="list-group-horizontal-body">
                    <div class="menu">

                        <div class="hidden-sm hidden-xs mb-2">
                            <div class="text-black">
                                <b>
                                    Marketplace Admin
                                </b>
                            </div>
                            <div class="text-size-12">
                                For your day to day operations
                            </div>
                        </div>

                        <menuItem localSection="/statistics" v-qa="'statistics-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/stats.svg" alt="">
                            </span>
                            <span>Statistics</span>
                        </menuItem>

                        <menuItem localSection="/applications" v-qa="'applicationList-link'" :class="{'text-danger': meta.applications}">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/profile.svg" alt="">
                            </span>
                            <span>Applications <span v-if="meta.applications" class="badge badge-danger ml-1">{{meta.applications}}</span></span>
                        </menuItem>
                        <menuItem localSection="/retailers" v-qa="'retailerList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/shopping-basket.svg" alt="">
                            </span>
                            <span>Retailers</span>
                        </menuItem>
                        <menuItem localSection="/vendors" v-qa="'vendorList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/bank.svg" alt="">
                            </span>
                            <span>Vendors</span>
                        </menuItem>
                        <menuItem localSection="/stores" v-qa="'storeList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/store.svg" alt="">
                            </span>
                            <span>Stores</span>
                        </menuItem>
                        <menuItem localSection="/users" v-qa="'userList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/people.svg" alt="">
                            </span>
                            <span>Users</span>
                        </menuItem>
                        <menuItem localSection="/states" v-qa="'statesList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/shield.svg" alt="">
                            </span>
                            <span>Locations</span>
                        </menuItem>
                        <menuItem localSection="/required-docs" v-qa="'documentsList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/receipt.svg" alt="">
                            </span>
                            <span>Documents Required</span>
                        </menuItem>
                        <menuItem localSection="/regulators" v-qa="'regulatoryBodyList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/eye.svg" alt="">
                            </span>
                            <span>Regulators</span>
                        </menuItem>
                        <menuItem localSection="/products" v-qa="'productList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/products.svg" alt="">
                            </span>
                            <span>Products</span>
                        </menuItem>
                        <menuItem localSection="/retailer-orders" v-qa="'retailerOrderList-link'" :class="{'text-danger': meta.retailerOrders}">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/cart-empty.svg" alt="">
                            </span>
                            <span>Retailer Orders <span v-if="meta.retailerOrders" class="badge badge-danger ml-1">{{meta.retailerOrders}}</span></span>
                        </menuItem>
                        <menuItem localSection="/vendor-orders" v-qa="'vendorOrderList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/cart-full.svg" alt="">
                            </span>
                            <span>Vendor Orders</span>
                        </menuItem>
                        <menuItem localSection="/product-orders" v-qa="'productOrderList-link'" :class="{'text-danger': meta.productOrders}">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/receipt.svg" alt="">
                            </span>
                            <span>Product Orders <span v-if="meta.productOrders" class="badge badge-danger ml-1">{{meta.productOrders}}</span></span>
                        </menuItem>
                        <!--<menuItem localSection="/transactions">-->
                            <!--<span class="mr-1">-->
                                <!--<svg class="icon" viewBox="0 0 24 24">-->
                                    <!--<path d="M9.01 14H2V16H9.01V19L13 15 9.01 11V14ZM14.99 13V10H22V8H14.99V5L11 9 14.99 13Z" fill-rule="evenOdd"></path>-->
                                <!--</svg>-->
                            <!--</span>-->
                            <!--<span>Transactions</span>-->
                        <!--</menuItem>-->
                        <!--<menuItem localSection="/payouts">-->
                            <!--<span class="mr-1">-->
                                <!--<svg class="icon" viewBox="0 0 24 24">-->
                                    <!--<path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13.41 18.09V20H10.74V18.07C9.03 17.71 7.58 16.61 7.47 14.67H9.43C9.53 15.72 10.25 16.54 12.08 16.54 14.04 16.54 14.48 15.56 14.48 14.95 14.48 14.12 14.04 13.34 11.81 12.81 9.33 12.21 7.63 11.19 7.63 9.14 7.63 7.42 9.02 6.3 10.74 5.93V4H13.41V5.95C15.27 6.4 16.2 7.81 16.26 9.34H14.3C14.25 8.23 13.66 7.47 12.08 7.47 10.58 7.47 9.68 8.15 9.68 9.11 9.68 9.95 10.33 10.5 12.35 11.02S16.53 12.41 16.53 14.93C16.52 16.76 15.15 17.76 13.41 18.09Z" fill-rule="evenOdd"></path>-->
                                <!--</svg>-->
                            <!--</span>-->
                            <!--<span>Payouts</span>-->
                        <!--</menuItem>-->
                        <menuItem localSection="/tickets" v-qa="'ticketList-link'" :class="{'text-danger': meta.tickets}">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/mail-empty.svg" alt="">
                            </span>
                            <span>Tickets <span v-if="meta.tickets" class="badge badge-danger ml-1">{{meta.tickets}}</span></span>
                        </menuItem>
                        <menuItem localSection="/reviews" v-qa="'reviewList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/review.svg" alt="">
                            </span>
                            <span>Reviews</span>
                        </menuItem>
                        <!--<menuItem localSection="/shippers">-->
                            <!--<span class="mr-1">-->
                                <!--<svg class="icon" viewBox="0 0 24 24">-->
                                    <!--<path d="M16.36,4.27H18.55V2.13H16.36V1.07H18.22C17.89,0.43 17.13,0 16.36,0C15.16,0 14.18,0.96 14.18,2.13C14.18,3.31 15.16,4.27 16.36,4.27M10.04,9.39L13,6.93L17.45,9.6H10.25M19.53,12.05L21.05,10.56C21.93,9.71 21.93,8.43 21.05,7.57L19.2,9.39L13.96,4.27C13.64,3.73 13,3.41 12.33,3.41C11.78,3.41 11.35,3.63 11,3.95L7,7.89C6.65,8.21 6.44,8.64 6.44,9.17V9.71H5.13C4.04,9.71 3.16,10.67 3.16,11.84V12.27C3.5,12.16 3.93,12.16 4.25,12.16C7.09,12.16 9.5,14.4 9.5,17.28C9.5,17.6 9.5,18.03 9.38,18.35H14.5C14.4,18.03 14.4,17.6 14.4,17.28C14.4,14.29 16.69,12.05 19.53,12.05M4.36,19.73C2.84,19.73 1.64,18.56 1.64,17.07C1.64,15.57 2.84,14.4 4.36,14.4C5.89,14.4 7.09,15.57 7.09,17.07C7.09,18.56 5.89,19.73 4.36,19.73M4.36,12.8C1.96,12.8 0,14.72 0,17.07C0,19.41 1.96,21.33 4.36,21.33C6.76,21.33 8.73,19.41 8.73,17.07C8.73,14.72 6.76,12.8 4.36,12.8M19.64,19.73C18.11,19.73 16.91,18.56 16.91,17.07C16.91,15.57 18.11,14.4 19.64,14.4C21.16,14.4 22.36,15.57 22.36,17.07C22.36,18.56 21.16,19.73 19.64,19.73M19.64,12.8C17.24,12.8 15.27,14.72 15.27,17.07C15.27,19.41 17.24,21.33 19.64,21.33C22.04,21.33 24,19.41 24,17.07C24,14.72 22.04,12.8 19.64,12.8Z" fill-rule="evenOdd"></path>-->
                                <!--</svg>-->
                            <!--</span>-->
                            <!--<span>Shippers</span>-->
                        <!--</menuItem>-->
                        <!--<menuItem localSection="/shipments">-->
                            <!--<span class="mr-1">-->
                                <!--<svg class="icon" viewBox="0 0 24 24">-->
                                    <!--<path d="M20 8H17V4H3C1.9 4 1 4.9 1 6V17H3C3 18.66 4.34 20 6 20S9 18.66 9 17H15C15 18.66 16.34 20 18 20S21 18.66 21 17H23V12L20 8ZM6 18.5C5.17 18.5 4.5 17.83 4.5 17S5.17 15.5 6 15.5 7.5 16.17 7.5 17 6.83 18.5 6 18.5ZM19.5 9.5L21.46 12H17V9.5H19.5ZM18 18.5C17.17 18.5 16.5 17.83 16.5 17S17.17 15.5 18 15.5 19.5 16.17 19.5 17 18.83 18.5 18 18.5Z" fill-rule="evenOdd"></path>-->
                                <!--</svg>-->
                            <!--</span>-->
                            <!--<span>Shipments</span>-->
                        <!--</menuItem>-->
                        <div class="hidden-sm hidden-xs mt-6 mb-2">
                            <div class="text-black">
                                <b>
                                    Tools
                                </b>
                            </div>
                            <div class="text-size-12">
                                Features of the surrounding environment.
                            </div>
                        </div>
                        <menuItem localSection="/tools/export" v-qa="'export-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/file-export.svg" alt="">
                            </span>
                            <span>Export</span>
                        </menuItem>

                        <div class="hidden-sm hidden-xs mt-6 mb-2">
                            <div class="text-black">
                                <b>
                                    Email Blasts
                                </b>
                            </div>
                            <div class="text-size-12">
                                Send bulk correspondence to selected groups
                            </div>
                        </div>
                        <menuItem localSection="/email-blasts" v-qa="'emailBlastList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/bomb.svg" alt="">
                            </span>
                            <span>Blasts</span>
                        </menuItem>
                        <menuItem localSection="/email-templates" v-qa="'emailBlastTemplateList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/code.svg" alt="">
                            </span>
                            <span>Templates</span>
                        </menuItem>
                        <menuItem localSection="/audiences" v-qa="'emailBlastAudienceList-link'">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/people.svg" alt="">
                            </span>
                            <span>Audiences</span>
                        </menuItem>

                        <div class="hidden-sm hidden-xs mt-6 mb-2">
                            <div class="text-black">
                                <b>
                                    Developers
                                </b>
                            </div>
                            <div class="text-size-12">
                                To simplify development and support
                            </div>
                        </div>

                        <menuItem localSection="/tools" v-if="!isProduction">
                            <span class="mr-1">
                                <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/wrench.svg" alt="">
                            </span>
                            <span>Tools</span>
                        </menuItem>
                        <a href="/admin/queue/" v-if="!isProduction" class="menu-link list-group-horizontal-item">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/queue.svg" alt="">
                            </span>
                                <span>Queue</span>
                            </span>
                        </a>
                        <a href="http://localhost:3201/mailbox/#/" v-if="!isProduction" class="menu-link list-group-horizontal-item" target="_blank">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/mail.svg" alt="">
                            </span>
                                <span>Mailbox</span>
                            </span>
                        </a>
                        <a href="/admin/emails/index.html" v-if="!isProduction" class="menu-link list-group-horizontal-item" target="_blank">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/email-open-outline.svg" alt="">
                                </span>
                                <span>Email Templates</span>
                            </span>
                        </a>

                        <div class="hidden-sm hidden-xs mt-6 mb-2">
                            <div class="text-black">
                                <b>
                                    Support
                                </b>
                            </div>
                            <div class="text-size-12">
                                Report issues or provide feedback
                            </div>
                        </div>


                        <a class="menu-link list-group-horizontal-item" href="https://trello.com/b/mGZXZS7O" target="_blank">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/lightbulb-on.svg" alt="">
                                </span>
                                <span>Issues & Ideas</span>
                            </span>
                        </a>

                        <a class="menu-link list-group-horizontal-item" href="https://canideal-buyers.groovehq.com/help" target="_blank">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/help.svg" alt="">
                                </span>
                                <span>
                                    Retailer Helpdesk
                                </span>
                            </span>
                        </a>

                        <a class="menu-link list-group-horizontal-item" href="https://canideal-vendors.groovehq.com/help" target="_blank">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/help.svg" alt="">
                                </span>
                                <span>
                                    Vendor Helpdesk
                                </span>
                            </span>
                        </a>

                        <a class="menu-link list-group-horizontal-item" href="http://canideal.1password.com" target="_blank">
                            <span class="d-flex">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/lock.svg" alt="">
                                </span>
                                <span>Passwords</span>
                            </span>
                        </a>

                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import {mapGetters, mapState} from 'vuex';
import MenuItem from './MenuItem';

export default {
    name: 'Menu',
    components: {
        MenuItem
    },
    computed: {
        ...mapState(['meta']),
        ...mapGetters(['isProduction'])
    }
};
</script>
