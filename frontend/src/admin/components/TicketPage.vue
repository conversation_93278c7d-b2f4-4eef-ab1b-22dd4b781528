<template>
    <div>
        <div class="panel panel-default mb-0">
            <div class="panel-heading">
                <h4 class="panel-title text-700">
                    <router-link to="/tickets" class="text-link"><span class="text-link">Tickets</span>
                    </router-link>
                    <svg class="icon text-muted" viewBox="0 0 24 24">
                        <path d="M8.59 16.34L13.17 11.75 8.59 7.16 10 5.75 16 11.75 10 17.75Z"
                              fill-rule="evenOdd"></path>
                    </svg>
                    {{$route.params.key}}
                </h4>
            </div>
            <div class="panel-body" v-if="!showNotFound && !loading">

                <div class="row-condensed">
                    <div class="col-sm-8">

                        <div class="table-responsive">
                            <table class="table table-bordered mb-0">
                                <tbody>
                                <tr>
                                    <td class="w-30">
                                        Ticket ID
                                    </td>
                                    <td>
                                        {{ticket.key}}
                                    </td>
                                </tr>
                                <tr v-if="ticket.productOrder">
                                    <td>
                                        Product Order ID
                                    </td>
                                    <td>
                                        <router-link :to="'/product-orders/' + ticket.productOrder.key">
                                            {{ticket.productOrder.key}}
                                        </router-link>
                                    </td>
                                </tr>
                                <tr v-if="ticket.category">
                                    <td>
                                        Category
                                    </td>
                                    <td>
                                        <template v-if="ticket.category === 'account_issues'">Account Issues</template>
                                        <template v-else-if="ticket.category === 'login_problems'">Login Problems</template>
                                        <template v-else-if="ticket.category === 'product_management'">Product Management</template>
                                        <template v-else-if="ticket.category === 'payment_issues'">Payment Issues</template>
                                        <template v-else-if="ticket.category === 'general_support'">General Support</template>
                                        <template v-else-if="ticket.category === 'order_related'">Order Related</template>
                                        <template v-else>{{ticket.category}}</template>
                                    </td>
                                </tr>
                                <tr v-if="ticket.retailer">
                                    <td>
                                        Retailer Name
                                    </td>
                                    <td>
                                        <router-link :to="'/retailers/'+ticket.retailer.key">
                                            {{ticket.retailer.businessName}}
                                        </router-link>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Vendor Name
                                    </td>
                                    <td>
                                        <router-link :to="'/vendors/'+ticket.vendor.key">
                                            {{ticket.vendor.storeName}}
                                        </router-link>
                                    </td>
                                </tr>

                                <tr v-if="ticket.product">
                                    <td>
                                        Product Name
                                    </td>
                                    <td>
                                        <router-link :to="'/products/'+ticket.product.key">
                                            {{ticket.product.name}}
                                        </router-link>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Subject
                                    </td>
                                    <td class="text-gray-dark">
                                        {{ticket.subject}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="form-control-static">Status</div>
                                    </td>
                                    <td>
                                        <div class="form-control-select">
                                            <select class="form-control" v-model="ticket.status">
                                                <option value="opened">Opened</option>
                                                <option value="closed">Closed</option>
                                            </select>
                                            <span class="form-control-select-arrow">
                                                <span class="caret"></span>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Created At
                                    </td>
                                    <td>
                                        <div class="text-nowrap">{{ticket.createdAt | date}}</div>
                                        <div class="text-size-13 opacity-60">
                                            {{ticket.createdAt | time}}
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Updated At
                                    </td>
                                    <td>
                                        <div class="text-nowrap">{{ticket.updatedAt | date}}</div>
                                        <div class="text-size-13 opacity-60">
                                            {{ticket.updatedAt | time}}
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Closed At
                                    </td>
                                    <td>
                                        <template v-if="ticket.closedAt">
                                            <div class="text-nowrap">{{ticket.closedAt | date}}</div>
                                            <div class="text-size-13 opacity-60">
                                                {{ticket.closedAt | time}}
                                            </div>
                                        </template>
                                        <template v-else>–</template>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        Opened By
                                    </td>
                                    <td class="text-capitalize">
                                        {{ticket.openedBy || '–'}}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row-condensed">
                    <div class="col-sm-8">
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-width-xs btn-xs-block" @click="save()">
                                Save
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <ticket-chat :ticket="ticket" show-for="admin" @send="sendMessage" class="mt-3"></ticket-chat>
                </div>


            </div>
            <div v-else-if="!loading">
                <h2>Product Not Found</h2>
            </div>
        </div>
    </div>
</template>

<script>
import TicketChat from '@/common/components/TicketChat';

export default {
    name: 'TicketPage',
    inject: ['$ticketService'],
    components: {TicketChat},
    data() {
        return {
            loading: true,
            showNotFound: false,
            ticket: {}
        };
    },
    created() {
        this.$ticketService.getTicketById(this.$route.params.key)
            .then(ticket => {
                this.ticket = {...ticket};
                this.loading = false;
            })
            .catch(() => {
                this.showNotFound = true;
            });
    },
    computed: {},
    methods: {
        save() {
            this.$ticketService.updateTicket(this.ticket.key, {status: this.ticket.status})
                .then(ticket => {
                    this.ticket = ticket;
                    this.$flashSuccess('Changes has been saved successfully.');
                })
                .catch(err => {
                    this.$flashError(err);
                });
        },
        sendMessage(text) {
            return this.$ticketService.sendMessageToTicket(this.ticket.key, {text})
                .then(ticket => {
                    this.ticket = ticket;
                });
        }
    }
};
</script>
