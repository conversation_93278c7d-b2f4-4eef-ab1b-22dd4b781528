<template>
    <smart-table :data="ticketList" @update="getTicketList" class="panel panel-default mb-0">
        <template slot="caption">
            <div class="panel-heading">
                <h4 class="panel-title text-700" v-qa="'ticketList-page'">
                    Tickets
                </h4>
            </div>
            <div class="px-3 pt-3">
                <div class="d-flex d-xs-block align-items-center mb-3">
                    <div>
                        <div class="form-control-select">
                            <select class="form-control" v-st-filter="'status'">
                                <option value="">All Statuses</option>
                                <option value="opened">Opened</option>
                                <option value="closed">Closed</option>
                            </select>
                            <span class="form-control-select-arrow">
                                <span class="caret"></span>
                            </span>
                        </div>
                    </div>
                    <div class="mr-2 hidden-xs"></div>
                    <div class="mt-2 visible-xs-block"></div>
                    <div>
                        <input type="text" class="form-control" placeholder="Search Product Order" v-st-filter:input="'q-order'">
                    </div>
                    <div class="mr-2 hidden-xs"></div>
                    <div class="mt-2 visible-xs-block"></div>
                    <div>
                        <input type="text" class="form-control" placeholder="Search Vendor" v-st-filter:input="'q-vendor'">
                    </div>
                    <div class="mr-2 hidden-xs"></div>
                    <div class="mt-2 visible-xs-block"></div>
                    <div>
                        <input type="text" class="form-control" placeholder="Search Retailer" v-st-filter:input="'q-retailer'">
                    </div>
                    <div class="mr-2 hidden-xs"></div>
                    <div class="mt-2 visible-xs-block"></div>
                    <div>
                        <input type="text" class="form-control" placeholder="Search Product" v-st-filter:input="'q-product'">
                    </div>
                    <div class="mr-2 hidden-xs"></div>
                    <div class="mt-2 visible-xs-block"></div>
                    <div>
                        <span class="btn btn-default" v-st-reset>Reset</span>
                    </div>
                </div>

            </div>
        </template>
        <template slot="header">
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="key">
                    <div class="text-nowrap">
                        Ticket ID
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="productOrder.key">
                    <div class="text-nowrap">
                        Product Order ID
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="vendor.storeName">
                    <div class="text-nowrap">
                        Vendor Name
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="category">
                    <div class="text-nowrap">
                        Category
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="retailer.businessName">
                    <div class="text-nowrap">
                        Type
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="product.name">
                    <div class="text-nowrap">
                        Product Name
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="status">
                    <div class="text-nowrap">
                        Status
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="createdAt">
                    <div class="text-nowrap">
                        Created At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="updatedAt">
                    <div class="text-nowrap">
                        Updated At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
        </template>

        <template slot="row" slot-scope="{item}">
            <router-link :to="`/tickets/${item.key}`" tag="tr" class="cursor-pointer"
                         :class="{'danger': item.status === 'opened'}">
                <td>
                    {{item.key}}
                </td>
                <td>
                    {{item.productOrder && item.productOrder.key || '-'}}
                </td>
                <td>
                    <div class="text-md-wrap">
                        {{item.vendor.storeName}}
                    </div>
                </td>
                <td>
                    <div class="text-md-wrap">
                        <template v-if="item.category === 'account_issues'">Account Issues</template>
                        <template v-else-if="item.category === 'login_problems'">Login Problems</template>
                        <template v-else-if="item.category === 'product_management'">Product Management</template>
                        <template v-else-if="item.category === 'payment_issues'">Payment Issues</template>
                        <template v-else-if="item.category === 'general_support'">General Support</template>
                        <template v-else-if="item.category === 'order_related'">Order Related</template>
                        <template v-else>{{item.category || 'Order Related'}}</template>
                    </div>
                </td>
                <td>
                    <div class="text-md-wrap">
                        <template v-if="item.retailer">
                            {{item.retailer.businessName}}
                        </template>
                        <template v-else>
                            <span class="text-info">Support</span>
                        </template>
                    </div>
                </td>
                <td>
                    <div class="text-md-wrap">
                        <template v-if="item.product">
                            {{item.product.name}}
                        </template>
                        <template v-else>
                            <span class="text-muted">–</span>
                        </template>
                    </div>
                </td>
                <td class="text-capitalize">
                    {{item.status}}
                </td>
                <td>
                    <div class="text-nowrap">{{item.createdAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.createdAt | time}}
                    </div>
                </td>
                <td>
                    <div class="text-nowrap">{{item.updatedAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.updatedAt | time}}
                    </div>
                </td>
            </router-link>
        </template>

        <template slot="emptyPromo">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg class="icon icon-3x opacity-50" viewBox="0 0 24 24">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                No tickets found
            </div>
        </template>
        <template slot="empty">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg class="icon icon-3x opacity-50" viewBox="0 0 24 24">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                No tickets found
            </div>
        </template>
    </smart-table>
</template>

<script>
import {mapState} from 'vuex';

export default {
    name: 'TicketListPage',
    inject: ['$ticketService'],
    data() {
        const ticketList = [];
        return {
            ticketList
        };
    },
    computed: {
        ...mapState(['currentState'])
    },
    methods: {
        getTicketList({filters, pagination}) {
            const promise = this.$ticketService.getTicketList({...filters, ...pagination, state: this.currentState})
                .then(ticketList => {
                    this.ticketList = ticketList;
                });
            this.$root.$emit('loading', promise);
            promise
                .catch(err => {
                    this.$flashError(err);
                });
        }
    }
};
</script>
