<template>
    <smart-table :data="vendorList" @update="getVendorList" class="panel panel-default mb-0">
        <template slot="caption">
            <div class="panel-heading">
                <h4 class="panel-title text-700" v-qa="'vendorList-page'">
                    Vendors
                </h4>
            </div>
            <div class="px-3 pt-3">
                <div class="mb-3">
                    <div class="d-flex flex-wrap m-xs-minus-1 d-xs-block align-items-center">
                        <div class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'enabled'">
                                    <option :value="true">User Enabled Account: Yes</option>
                                    <option :value="false">User Enabled Account: No</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'applicationStatus'">
                                    <option :value="null">All Application Statuses</option>
                                    <option value="noSubmitted">Not submitted</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Accepted">Accepted</option>
                                    <option value="Declined">Declined</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>

<!--start cvv added-->
                    <div style="flex-basis: 230px;" class="m-xs-1 mb-2">
                        <div class="form-control-select">
                            <select class="form-control" v-st-filter="'complianceStatus'">
                                <option :value="null">All Compliance Statuses</option>
                                <option value="compliant">Compliant</option>
                                <option value="unverified">Unverified</option>
                                <option value="expired">Expired</option>
                            </select>
                            <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                        </div>
                    </div>
<!--stop cvv added-->

                        <div style="flex-basis: 155px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'products'">
                                    <option :value="null">Any Products</option>
                                    <option value="zero">0</option>
                                    <option value="betweenOneAndTen">1 - 10</option>
                                    <option value="aboveTen">10+</option>
                                    <option value="allActive">All Active</option>
                                    <option value="allInactive">All Inactive</option>
                                    <option value="aboveHalfHaveImages">50%+ Have Images</option>
                                    <option value="lessHalfHaveImages">50% Don't Have Images</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div style="flex-basis: 180px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'lastTermsDate'">
                                    <option :value="null">Any Terms Dates</option>
                                    <option value="accepted">Terms Accepted</option>
                                    <option value="notAccepted">Terms Not Accepted</option>
                                    <template v-if="lastTermsDate">
                                        <option value="notAcceptedLast">Not Accepted After Terms Changed On {{lastTermsAt}}</option>
                                        <option value="acceptedLast">Accepted After Terms Changed On {{lastTermsAt}}</option>
                                    </template>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div style="flex-basis: 180px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'ein'">
                                    <option :value="null">All EIN/Tax ID</option>
                                    <option value="provided">Provided</option>
                                    <option value="not_provided">Not Provided</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div class="m-xs-1 mb-2">
                            <input type="text" class="form-control" placeholder="Search name or email"
                                   v-qa="'search'"
                                   v-st-filter:input="'q'">
                        </div>
                        <div class="m-xs-1 mb-2">
                            <span class="btn btn-default" v-st-reset>Reset</span>
                        </div>
                    </div>
                </div>

            </div>
        </template>
        <template slot="header">
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="key">
                    <div>
                        ID
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="text-gray-dark">
                <div>
                    State/Country
                </div>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="application.status">
                    <div class="text-nowrap">
                        Application Status
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="storeName">
                    <div class="text-nowrap">
                        Vendor Name
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>

<!--start cvv added-->
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="complianceStatus">
                    <div class="text-nowrap">
                        Compliance Status
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
<!--stop cvv added-->

            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="lastAgreeTermsAt">
                    <div class="text-nowrap">
                        Last Accepted Terms At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="createdAt">
                    <div>
                        Created At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
        </template>

        <template slot="row" slot-scope="{item}">
            <router-link :to="`/vendors/${item.key}`" tag="tr" class="cursor-pointer"
                         :class="{'danger': item.application.status === 'Draft' || item.application.status === 'Pending'}">
                <td v-qa="item.key">
                    {{item.key}}
                </td>
                <td>
                    <state :value="item.state"/>
                </td>
                <td>
                    {{item.application.status || 'Not submitted'}}
                </td>
                <td>
                    <div class="text-md-wrap">
                        {{item.storeName}}
                    </div>
                </td>

<!--start cvv added-->
                <td>
                    Valid
                    <div class="text-size-13 opacity-60">
                        till Apr 25 2022
                    </div>
                </td>
<!--stop cvv added-->


                <td>
                    <div class="text-nowrap">{{item.lastAgreeTermsAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.lastAgreeTermsAt | time}}
                    </div>
                </td>
                <td>
                    <div class="text-nowrap">{{item.createdAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.createdAt | time}}
                    </div>
                </td>
            </router-link>
        </template>

        <template slot="emptyPromo">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg class="icon icon-3x opacity-50" viewBox="0 0 24 24">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                Not found
            </div>
        </template>
        <template slot="empty">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg class="icon icon-3x opacity-50" viewBox="0 0 24 24">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                No Vendors yet
            </div>
        </template>
    </smart-table>
</template>

<script>
import {mapState} from 'vuex';
import {DateTime} from 'luxon';

export default {
    name: 'VendorListPage',
    inject: ['$vendorService'],
    data() {
        const vendorList = [];
        return {
            vendorList
        };
    },
    computed: {
        ...mapState(['currentState', 'lastTermsDate']),
        lastTermsAt() {
            if (!this.lastTermsDate) {
                return '';
            }
            return DateTime.fromISO(this.lastTermsDate).toFormat('MMM dd, yyyy');
        }
    },
    methods: {
        getVendorList({filters, pagination}) {
            const promise = this.$vendorService.getVendorList({...filters, ...pagination, state: this.currentState})
                .then(vendorList => {
                    this.vendorList = vendorList;
                });
            this.$root.$emit('loading', promise);
            promise
                .catch(err => {
                    this.$flashError(err);
                });
        }
    }
};
</script>
