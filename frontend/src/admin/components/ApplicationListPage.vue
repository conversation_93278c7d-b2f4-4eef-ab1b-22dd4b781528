<template>
    <div>
        <!-- Duplicate Cleanup Tool -->
        <div v-if="showDuplicateCleanup" class="panel panel-default mb-3" style="margin-top: 20px;">
            <div class="panel-heading">
                <h4 class="panel-title text-700">
                    <i class="fa fa-tools"></i> Duplicate Application Cleanup Tool
                </h4>
            </div>
            <div class="panel-body">
                <div v-if="!duplicateData && !loadingDuplicates">
                    <p class="text-muted">
                        This tool helps identify and clean up duplicate applications that may have been created due to the previous bug.
                        It will analyze all vendors and retailers to find duplicates and recommend which applications to keep.
                    </p>
                    <button class="btn btn-primary" @click="loadDuplicates" :disabled="loadingDuplicates">
                        <i class="fa fa-search"></i> Analyze Duplicates
                    </button>
                </div>

                <div v-if="loadingDuplicates" class="text-center py-3">
                    <i class="fa fa-spinner fa-spin"></i> Analyzing applications...
                </div>

                <div v-if="duplicateData">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-warning">{{ duplicateData.summary.vendorsWithDuplicates }}</h4>
                                    <small>Vendors with Duplicates</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-warning">{{ duplicateData.summary.retailersWithDuplicates }}</h4>
                                    <small>Retailers with Duplicates</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-danger">{{ duplicateData.summary.totalDuplicateApplications }}</h4>
                                    <small>Total Duplicates to Remove</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="duplicateData.summary.totalDuplicateApplications > 0">
                        <div class="mb-3">
                            <button class="btn btn-outline-primary mr-2" @click="runCleanup(true)" :disabled="cleanupRunning">
                                <i class="fa fa-eye"></i> Preview Cleanup (Dry Run)
                            </button>
                            <button class="btn btn-danger" @click="runCleanup(false)" :disabled="cleanupRunning">
                                <i class="fa fa-trash"></i> Execute Cleanup
                            </button>
                        </div>

                        <div v-if="cleanupRunning" class="text-center py-3">
                            <i class="fa fa-spinner fa-spin"></i> {{ cleanupDryRun ? 'Previewing' : 'Executing' }} cleanup...
                        </div>

                        <div v-if="cleanupResults">
                            <div class="alert" :class="cleanupResults.dryRun ? 'alert-info' : 'alert-success'">
                                <h6>{{ cleanupResults.dryRun ? 'Cleanup Preview' : 'Cleanup Results' }}</h6>
                                <ul class="mb-0">
                                    <li>Vendors processed: {{ cleanupResults.vendorsProcessed }}</li>
                                    <li>Retailers processed: {{ cleanupResults.retailersProcessed }}</li>
                                    <li>Vendors fixed: {{ cleanupResults.vendorsFixed }}</li>
                                    <li>Retailers fixed: {{ cleanupResults.retailersFixed }}</li>
                                    <li>Applications deleted: {{ cleanupResults.applicationsDeleted }}</li>
                                    <li>Errors: {{ cleanupResults.errors.length }}</li>
                                </ul>
                            </div>

                            <div v-if="cleanupResults.errors.length > 0" class="alert alert-danger">
                                <h6>Errors:</h6>
                                <ul class="mb-0">
                                    <li v-for="error in cleanupResults.errors" :key="error.key">
                                        {{ error.entity }} {{ error.key }}: {{ error.error }}
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Detailed view of duplicates -->
                        <div class="mt-4">
                            <h6>Vendors with Duplicates:</h6>
                            <div v-for="vendor in duplicateData.vendors" :key="vendor.key" class="card mb-2">
                                <div class="card-body">
                                    <h6>{{ vendor.storeName }} ({{ vendor.key }})</h6>
                                    <p class="text-muted mb-2">
                                        Current: {{ vendor.currentApplication || 'None' }} ({{ vendor.currentApplicationStatus || 'N/A' }})
                                        → Recommended: {{ vendor.recommendedApplication }}
                                    </p>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small><strong>Keep:</strong> {{ vendor.recommendedApplication }}</small>
                                        </div>
                                        <div class="col-md-6">
                                            <small><strong>Delete:</strong> {{ vendor.duplicatesToDelete.join(', ') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h6 class="mt-4">Retailers with Duplicates:</h6>
                            <div v-for="retailer in duplicateData.retailers" :key="retailer.key" class="card mb-2">
                                <div class="card-body">
                                    <h6>{{ retailer.storeName }} ({{ retailer.key }})</h6>
                                    <p class="text-muted mb-2">
                                        Current: {{ retailer.currentApplication || 'None' }} ({{ retailer.currentApplicationStatus || 'N/A' }})
                                        → Recommended: {{ retailer.recommendedApplication }}
                                    </p>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small><strong>Keep:</strong> {{ retailer.recommendedApplication }}</small>
                                        </div>
                                        <div class="col-md-6">
                                            <small><strong>Delete:</strong> {{ retailer.duplicatesToDelete.join(', ') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-else class="alert alert-success">
                        <i class="fa fa-check"></i> No duplicate applications found! Your database is clean.
                    </div>
                </div>
            </div>
        </div>

        <smart-table :data="applicationList" @update="getApplications" class="panel panel-default mb-0">
        <template slot="caption">
            <div class="panel-heading">
                <h4 class="panel-title text-700" v-qa="'applicationList-page'">
                    Applications
                </h4>
            </div>
            <div class="px-3 pt-3">
                <div class="mb-3">
                    <div class="d-flex flex-wrap m-xs-minus-1 d-xs-block align-items-center">
                        <div style="flex-basis: 120px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'type'">
                                    <option :value="null">All Types</option>
                                    <option value="vendor">Vendor</option>
                                    <option value="retailer">Retailer</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div style="flex-basis: 230px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'status'">
                                    <option :value="null">All Application Statuses</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Accepted">Accepted</option>
                                    <option value="Declined">Declined</option>
                                    <option value="Expired">Expired</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div style="flex-basis: 180px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'ein'">
                                    <option :value="null">All EIN/Tax ID</option>
                                    <option value="provided">Provided</option>
                                    <option value="not_provided">Not Provided</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div style="flex-basis: 180px;" class="m-xs-1 mb-2">
                            <div class="form-control-select">
                                <select class="form-control" v-st-filter="'lastTermsDate'">
                                    <option :value="null">Any Terms Dates</option>
                                    <option value="accepted">Terms Accepted</option>
                                    <option value="notAccepted">Terms Not Accepted</option>
                                    <template v-if="lastTermsDate">
                                        <option value="notAcceptedLast">Not Accepted After Terms Changed On {{lastTermsAt}}</option>
                                        <option value="acceptedLast">Accepted After Terms Changed On {{lastTermsAt}}</option>
                                    </template>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                        <div class="m-xs-1 mb-2">
                            <input type="text" class="form-control" placeholder="Search Vendor" v-st-filter:input="'q-vendor'">
                        </div>
                        <div class="m-xs-1 mb-2">
                            <input type="text" class="form-control" placeholder="Search Retailer" v-st-filter:input="'q-retailer'">
                        </div>
                        <div class="m-xs-1 mb-2">
                            <input type="text" class="form-control" placeholder="Search Email" v-st-filter:input="'q'">
                        </div>
                        <div class="m-xs-1 mb-2">
                            <span class="btn btn-default" v-st-reset>Reset</span>
                        </div>
                        <div class="m-xs-1 mb-2">
                            <button class="btn btn-warning" @click="showDuplicateCleanup = !showDuplicateCleanup" style="background-color: #ffc107; border-color: #ffc107;">
                                <i class="fa fa-wrench"></i> {{ showDuplicateCleanup ? 'Hide' : 'Show' }} Cleanup Tool
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template slot="header">
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="type">
                    <div>
                        Type
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="status">
                    <div class="text-nowrap">
                        Application Status
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="state">
                    <div>
                        State/Country
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="businessName">
                    <div>
                        Vendor Name / Retailer Name
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>

            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="submittedAt">
                    <div class="text-nowrap">
                        Created At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="processedAt">
                    <div class="text-nowrap">
                        Processed At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
        </template>

        <template slot="row" slot-scope="{item}">
            <router-link :to="`/applications/${item.key}`" tag="tr" class="cursor-pointer"
                         :class="{'danger': item.status === 'Draft' || item.status === 'Pending'}">
                <td class="text-capitalize">
                    {{item.type}}
                </td>
                <td>
                    <template v-if="item.status === 'Pending'">
                        <span class="text-brand-warning">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2ZM16.2 16.2L11 13V7H12.5V12.2L17 14.9 16.2 16.2Z"
                                      fill-rule="evenodd"></path>
                            </svg>
                        </span>
                    </template>
                    <template v-if="item.status === 'Accepted'">
                        <span class="text-brand-success">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM10 17L5 12 6.41 10.59 10 14.17 17.59 6.58 19 8 10 17Z"
                                      fill-rule="evenodd"></path>
                            </svg>
                        </span>
                    </template>
                    <template v-if="item.status === 'Declined'">
                        <span class="text-brand-danger">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12 2C6.47 2 2 6.47 2 12S6.47 22 12 22 22 17.53 22 12 17.53 2 12 2ZM17 15.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59Z"
                                      fill-rule="evenodd"></path>
                            </svg>
                        </span>
                    </template>
                    {{item.status}}
                </td>
                <td>
                    <state :value="item.state"/>
                </td>
                <td>
                    {{item.businessName}}
                </td>
                <td>
                    <div class="text-nowrap">{{item.submittedAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.submittedAt | time}}
                    </div>
                </td>
                <td>
                    <template v-if="item.processedAt">
                        <div class="text-nowrap">{{item.processedAt | date}}</div>
                        <div class="text-size-13 opacity-60">
                            {{item.processedAt | time}}
                        </div>
                    </template>
                    <template v-else>-</template>
                </td>
            </router-link>
        </template>

        <template slot="emptyPromo">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg class="icon icon-3x opacity-50" viewBox="0 0 24 24">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                Not applications found
            </div>
        </template>
        <template slot="empty">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg class="icon icon-3x opacity-50" viewBox="0 0 24 24">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                Not applications found
            </div>
        </template>
    </smart-table>
    </div>
</template>

<script>
import {DateTime} from 'luxon';
import {mapState} from 'vuex';

export default {
    name: 'ApplicationListPage',
    inject: ['$applicationService'],
    filters: {
        state(value) {
            return value;
        }
    },
    data() {
        const applicationList = [];
        return {
            applicationList,
            showDuplicateCleanup: false,
            loadingDuplicates: false,
            duplicateData: null,
            cleanupRunning: false,
            cleanupDryRun: true,
            cleanupResults: null
        };
    },
    computed: {
        ...mapState(['currentState', 'lastTermsDate']),
        lastTermsAt() {
            if (!this.lastTermsDate) {
                return '';
            }
            return DateTime.fromISO(this.lastTermsDate).toFormat('MMM dd, yyyy');
        }
    },
    methods: {
        getApplications({filters, pagination}) {
            const promise = this.$applicationService.getApplications({...filters, ...pagination, state: this.currentState})
                .then(applicationList => {
                    this.applicationList = applicationList;
                });
            this.$root.$emit('loading', promise);
            promise
                .catch(err => {
                    this.$flashError(err);
                });
        },
        async loadDuplicates() {
            this.loadingDuplicates = true;
            this.duplicateData = null;

            try {
                const response = await this.$http.request({
                    url: '/admin/tools/applications/duplicates',
                    method: 'GET'
                });

                console.log('Duplicate applications response:', response);
                this.duplicateData = response;
            } catch (error) {
                console.error('Failed to load duplicate applications:', error);
                this.$flashError('Failed to load duplicate applications');
            } finally {
                this.loadingDuplicates = false;
            }
        },
        async runCleanup(dryRun = true) {
            this.cleanupRunning = true;
            this.cleanupDryRun = dryRun;
            this.cleanupResults = null;

            try {
                const response = await this.$http.request({
                    url: '/admin/tools/applications/cleanup',
                    method: 'POST',
                    data: { dryRun }
                });

                this.cleanupResults = response;

                if (!dryRun && response.applicationsDeleted > 0) {
                    this.$flashSuccess(`Cleanup completed! Fixed ${response.vendorsFixed + response.retailersFixed} entities and deleted ${response.applicationsDeleted} duplicate applications.`);

                    // Refresh the duplicate data
                    await this.loadDuplicates();
                }
            } catch (error) {
                console.error('Failed to run cleanup:', error);
                this.$flashError('Failed to run cleanup');
            } finally {
                this.cleanupRunning = false;
            }
        }
    }
};
</script>

<style scoped>

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    border-bottom: 1px solid rgba(0,0,0,.125);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.text-dark {
    color: #212529 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}
</style>
