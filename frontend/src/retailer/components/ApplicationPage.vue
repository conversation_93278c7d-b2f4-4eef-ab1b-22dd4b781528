<template>
    <div class="d-table-cell text-top">
        <div class="container my-6">
            <div class="row">
                <div class="col-lg-12">

                    <h2 v-if="application.status === 'create'" class="text-300 mt-0 mb-6 text-size-32">
                        Activate Account
                    </h2>
                    <h2 v-else-if="application.status === 'Draft'" class="text-300 mt-0 mb-6 text-size-32">
                        Finish Application
                    </h2>
                    <h2 v-else-if="application.status === 'Pending'" class="text-300 mt-0 mb-6 text-size-32">
                        Edit Application (Pending Review)
                    </h2>
                    <h2 v-else-if="application.status === 'Accepted'" class="text-300 mt-0 mb-6 text-size-32">
                        Edit Application (Approved)
                    </h2>
                    <h2 v-else class="text-300 mt-0 mb-6 text-size-32">
                        Edit Application
                    </h2>

                    <!-- Status notification for non-draft applications -->
                    <div v-if="application.status === 'Pending'" class="alert alert-info mb-4">
                        <strong>Application Status:</strong> Your application is currently under review. You can still make changes if needed.
                    </div>
                    <div v-else-if="application.status === 'Accepted'" class="alert alert-success mb-4">
                        <strong>Application Status:</strong> Your application has been approved! You can update your information if needed.
                    </div>

                    <div class="panel panel-default mb-0">
                        <div class="p-3 p-sm-6">
                            <application-form :application.sync="applicationForm" :states="states" :stateP="state"
                                :status="application.status" @send="send" @finish="finish" />
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApplicationForm from '@/retailer/components/ApplicationForm';
import { mapActions, mapState } from 'vuex';

export default {
    name: 'ApplicationPage',
    inject: ['$retailerService'],
    components: {
        ApplicationForm
    },
    data() {
        return {
            applicationForm: {
                businessName: '',
                tradeName: '',
                ein: '',
                legalAddress: {
                    line1: '',
                    line2: '',
                    city: '',
                    postal: '',
                    region: this.$store.getters.state.id
                },
                fullName: '',
                phone: '',
                description: '',
                monthlySales: '',
                license: {
                    cannabis: false,
                    number: '',
                    name: '',
                    licenseType: '',
                    businessName: '',
                    county: ''
                },
                files: [],
                proofOfAddress: undefined,
                personalId: undefined,
                agreeTerms: false
            },
            state: {},
            docExpired: true
        };
    },
    created() {
        this.state = this.$store.getters.state;

        this.$retailerService.getApplication()
            .then(({ ok, application }) => {
                if (ok && application.status === "Draft") {
                    this.applicationForm = application
                } else {
                    this.applicationForm.businessName = this.application.businessName ? this.application.businessName : this.retailer.storeName || '';
                    this.applicationForm.license.businessName = this.application.businessName ? this.application.businessName : this.retailer.storeName || '';
                    this.applicationForm.tradeName = this.application.tradeName || '';
                    this.applicationForm.ein = this.application.ein || '';
                    if (this.application.legalAddress) {
                        this.applicationForm.legalAddress = { ...this.application.legalAddress };
                    }
                    this.applicationForm.fullName = this.application.fullName || '';
                    this.applicationForm.phone = this.application.phone || '';
                    if (this.application.license && this.application.license.cannabis) {
                        this.applicationForm.license.cannabis = this.application.license.cannabis;
                    }
                    if (this.application.files && this.application.files.length > 0) {
                        this.applicationForm.files = this.application.files;
                    }
                    else {
                        this.applicationForm.files = [];
                    }
                }
            });


    },
    computed: {
        ...mapState(['states', 'retailer']),
        application() {
            return this.retailer.application || {};
        },
        saveDisabled() {
            return !this.agreeTerms;
        },
        requiredDocuments() {
            return this.state.requiredDocuments || [];
        },
        requiredDocIds() {
            return this.requiredDocuments.map(doc => doc._id);
        }
    },
    watch: {
        application({ status }) {
            if (status === "Pending") {
                this.$router.push('/search');
            }
        }
    },
    methods: {
        ...mapActions(['sendApplication', 'finishApplication']),
        send(isDraft) {
            // If application already exists (has an ID), update it instead of creating new
            const hasExistingApplication = this.application && (this.application._id || this.application.id);

            let promise;
            if (hasExistingApplication) {
                // Update existing application
                promise = this.finishApplication({
                    ...this.applicationForm,
                    agreeTermsOfServiceAndPrivacyPolicy: true,
                    agreeUserFeesAndPaymentPolicy: true,
                    agreeReturnPolicy: true,
                    forDraft: isDraft
                });
            } else {
                // Create new application
                promise = this.sendApplication({
                    ...this.applicationForm,
                    agreeTermsOfServiceAndPrivacyPolicy: true,
                    agreeUserFeesAndPaymentPolicy: true,
                    agreeReturnPolicy: true,
                    isDraft
                });
            }

            if (isDraft) {
                this.$emit('isSaving', promise);
            } else {
                this.$emit('loading', promise);
            }

            if (!isDraft) {
                promise
                    .then(() => {
                        this.$router.push('/');
                    })
                    .catch(err => {
                        this.$addErrors(err);
                    });
            }
            return;
        },
        finish(forDraft) {
            const promise = this.finishApplication({
                ...this.applicationForm,
                agreeTermsOfServiceAndPrivacyPolicy: true,
                agreeUserFeesAndPaymentPolicy: true,
                agreeReturnPolicy: true,
                forDraft
            });
            this.$emit('loading', promise);
            promise
                .then(() => {
                    this.$router.push('/');
                })
                .catch(err => {
                    this.$addErrors(err);
                });
        },
        checkDocExpiration() {
            if (this.application && this.application.files.length > 0) {
                for (let i = 0; i < this.application.files.length; i++) {
                    const file = this.application.files[i];
                    const docIndex = this.requiredDocIds.indexOf(file.docName);
                    if (docIndex !== -1 && this.requiredDocuments[docIndex].expirationRequired) {
                        const expirationDate = new Date(this.requiredDocuments[docIndex].expirationDate);
                        if (expirationDate < new Date()) {
                            this.docExpired = true; // set docExpired to true if an expired document is found
                            break; // exit loop if an expired document is found
                        }
                    }
                }

            }

        }
    }
};
</script>
