<template>
    <div class="page">
        <header-component></header-component>
        <coa-warning-banner></coa-warning-banner>

        <div class="d-table-row">
            <div class="d-table-cell text-top">
                <verify-email class="mt-6"></verify-email>
            </div>
        </div>

        <div class="d-table-row" v-if="isDomestic">
            <div class="d-table-cell text-top">
                <metrc-input-key class="mt-6"></metrc-input-key>
            </div>
        </div>

        <div class="content-row">
            <div class="d-table-cell text-top">
                <div class="container my-6">
                    <router-view :key="$route.path"></router-view>
                </div>
            </div>
        </div>

        <footer-component role="vendor"></footer-component>
        <modal-component ref="modal"></modal-component>

        <!-- AI Assistant (Inline) -->
        <div class="ai-assistant-inline">
            <button
                class="ai-assistant-btn-inline"
                @click="toggleAIAssistant"
                :class="{ 'active': aiAssistantOpen }"
                title="AI Assistant">
                🤖
            </button>

            <div v-if="aiAssistantOpen" class="ai-assistant-chat-inline">
                <div class="chat-header-inline">
                    <span>🤖 AI Assistant</span>
                    <button @click="closeAIAssistant" class="close-btn-inline">×</button>
                </div>

                <div class="chat-body-inline">
                    <div class="welcome-message-inline">
                        <p>👋 Hi! I'm your AI assistant. How can I help you today?</p>
                    </div>

                    <div v-if="aiMessages.length > 0" class="messages-inline">
                        <div
                            v-for="(message, index) in aiMessages"
                            :key="index"
                            class="message-inline"
                            :class="message.type">
                            <div class="message-content-inline">{{ message.content }}</div>
                        </div>
                    </div>

                    <div v-if="aiLoading" class="loading-inline">
                        <span>AI is thinking...</span>
                    </div>
                </div>

                <div class="chat-input-inline">
                    <input
                        v-model="aiCurrentMessage"
                        @keyup.enter="sendAIMessage"
                        placeholder="Ask me anything..."
                        class="message-input-inline"
                        :disabled="aiLoading">
                    <button
                        @click="sendAIMessage"
                        :disabled="!aiCurrentMessage.trim() || aiLoading"
                        class="send-btn-inline">
                        Send
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import FooterComponent from '@/common/components/Footer';
import VerifyEmail from '@/common/components/VerifyEmail';
import MetrcInputKey from '@/common/components/MetrcInputKey';
import { FlashService } from '@/common/FlashService';
import HttpPlugin from '@/common/plugins/httpPlugin';
import TermsAndConditionsModal from '@/vendor/components/TermsAndConditionsModal';
import Vue from 'vue';
import { sync } from 'vuex-router-sync';
import { mapGetters } from 'vuex';
import createRouter from '../router';
import store, {
    couponService,
    metrcProductService,
    orderService,
    productService,
    ticketService,
    userService,
    vendorService,
    loggerService
} from '../store';
import HeaderComponent from './Header';
import CoaWarningBanner from './CoaWarningBanner';

Vue.use(HttpPlugin, { store });

const router = createRouter(store);
router.afterEach((to) => {
    loggerService.pageView({ page: to.path });
});

sync(store, router, { moduleName: 'RouteModule' });
const flashService = new FlashService();

export default {
    name: 'VendorModule',
    data() {
        return {
            aiAssistantOpen: false,
            aiMessages: [],
            aiCurrentMessage: '',
            aiLoading: false
        };
    },
    provide: {
        $couponService: couponService,
        $userService: userService,
        $ticketService: ticketService,
        $productService: productService,
        $metrcProductService: metrcProductService,
        $orderService: orderService,
        $vendorService: vendorService,
        $logger: loggerService
    },
    components: {
        VerifyEmail,
        MetrcInputKey,
        HeaderComponent,
        FooterComponent,
        CoaWarningBanner
    },
    router,
    store,
    computed: {
        ...mapGetters(['state']),
        isDomestic() {
            return this.state.isDomestic;
        }
    },
    methods: {
        toggleAIAssistant() {
            this.aiAssistantOpen = !this.aiAssistantOpen;
            console.log('AI Assistant toggled:', this.aiAssistantOpen);
        },

        closeAIAssistant() {
            this.aiAssistantOpen = false;
        },

        async sendAIMessage() {
            if (!this.aiCurrentMessage.trim() || this.aiLoading) return;

            const userMessage = this.aiCurrentMessage.trim();
            this.aiMessages.push({
                type: 'user',
                content: userMessage,
                timestamp: new Date()
            });

            this.aiCurrentMessage = '';
            this.aiLoading = true;

            try {
                const response = await fetch('/ai-assistant/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: userMessage,
                        context: {
                            currentPage: this.$route.path,
                            userRole: 'vendor'
                        }
                    })
                });

                const data = await response.json();

                // Extract the AI response from the backend response structure
                const aiResponse = data.data?.message || data.message || data.response || 'I received your message!';

                this.aiMessages.push({
                    type: 'ai',
                    content: aiResponse,
                    timestamp: new Date()
                });
            } catch (error) {
                console.error('AI Assistant error:', error);
                this.aiMessages.push({
                    type: 'error',
                    content: 'Sorry, I encountered an error. Please try again.',
                    timestamp: new Date()
                });
            } finally {
                this.aiLoading = false;
            }
        }
    },
    mounted() {
        this.$modal.setTarget(this.$refs.modal);
        flashService.setTarget(this.$refs.flash);

        if (!store.state.shadowLogin && store.state.vendor.needAcceptNewTerms) {
            this.$modal.open(TermsAndConditionsModal, {
                size: 'modal-800',
                disableClose: true
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.ai-assistant-inline {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-assistant-btn-inline {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    cursor: pointer;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 20px rgba(23, 162, 184, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(23, 162, 184, 0.4);
    }

    &.active {
        background: #dc3545;
    }
}

.ai-assistant-chat-inline {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 450px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header-inline {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.close-btn-inline {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-body-inline {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.welcome-message-inline {
    margin-bottom: 16px;

    p {
        margin: 0;
        font-weight: 500;
    }
}

.messages-inline {
    margin-top: 16px;
}

.message-inline {
    margin-bottom: 12px;

    &.user {
        text-align: right;

        .message-content-inline {
            background: #17a2b8;
            color: white;
            display: inline-block;
            padding: 8px 12px;
            border-radius: 18px 18px 4px 18px;
            max-width: 80%;
        }
    }

    &.ai {
        text-align: left;

        .message-content-inline {
            background: #f1f3f4;
            color: #333;
            display: inline-block;
            padding: 8px 12px;
            border-radius: 18px 18px 18px 4px;
            max-width: 80%;
        }
    }

    &.error {
        text-align: left;

        .message-content-inline {
            background: #f8d7da;
            color: #721c24;
            display: inline-block;
            padding: 8px 12px;
            border-radius: 18px 18px 18px 4px;
            max-width: 80%;
        }
    }
}

.loading-inline {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 8px;
}

.chat-input-inline {
    padding: 16px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 8px;
}

.message-input-inline {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 8px 16px;
    outline: none;
    font-size: 14px;

    &:focus {
        border-color: #17a2b8;
    }

    &:disabled {
        background: #f8f9fa;
        cursor: not-allowed;
    }
}

.send-btn-inline {
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;

    &:hover:not(:disabled) {
        background: #138496;
    }

    &:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
}

@media (max-width: 480px) {
    .ai-assistant-chat-inline {
        width: calc(100vw - 40px);
        height: 60vh;
        bottom: 70px;
        right: -10px;
    }
}
</style>
