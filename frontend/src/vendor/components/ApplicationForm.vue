<template>
    <form class="form-horizontal" @submit.prevent="send(false)">
        <div class="register-stepper">
            <div v-for="(stepObj, index) in steps" :key="index" class="step"
                :class="{
                    'step-active': index === step,
                    'step-done': index < step && stepObj.validated,
                    'step-validated': stepObj.validated,
                    'step-invalid': !stepObj.validated && index < step,
                    'step-disabled': !canNavigateToStep(index)
                }"
                @click="goToStep(index)"
                :title="getStepTooltip(stepObj, index)">
                <span class="step-number">{{ index + 1 }}</span>
            </div>
        </div>

        <section v-show="step === 0">
            <form class="form" method="post" action="#" @submit.prevent="nextStep">
                <div class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                Business
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('businessName', 'step1') }">
                    <label class="control-label col-sm-4">
                        Business Name <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'The legal name of your business as registered with government authorities'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="businessName" v-model="businessNameModel"
                            v-qa="'businessName'" v-validate="'required'" data-vv-scope="step1" />
                    </div>
                </div>
                <div v-if="isDomestic" class="form-group" :class="{ 'has-error': errors.has('tradeName', 'step1') }">
                    <label class="control-label col-sm-4">
                        Trade Name <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'The name your business operates under (DBA - Doing Business As)'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="tradeName" v-model="application.tradeName"
                            v-qa="'tradeName'" v-validate="'required'" data-vv-scope="step1" />
                    </div>
                </div>

                <div v-if="isDomestic" class="form-group" :class="{ 'has-error': errors.has('ein', 'step1') }">
                    <label class="control-label col-sm-4">
                        <span>EIN/Tax ID <span class="text-danger">*</span></span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Your Employer Identification Number (EIN) or Tax ID from the IRS'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" placeholder="*********" class="form-control" name="ein"
                            v-model="application.ein" v-qa="'ein'" v-validate="{ required: true }" data-vv-scope="step1" />
                    </div>
                </div>

                <!-- License fields - ALWAYS REQUIRED (backend schema requirement) -->
                <div class="form-group" :class="{ 'has-error': errors.has('license.number', 'step1') }">
                    <label class="control-label col-sm-4">
                        <span>License Number <span class="text-danger">*</span></span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Your business license number from the regulatory authority'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" placeholder="*********" class="form-control" name="license.number"
                            v-model="application.license.number" v-qa="'license.number'" v-validate="{ required: true }"
                            data-vv-scope="step1" />
                    </div>
                </div>

                <div class="form-group" :class="{ 'has-error': errors.has('license.licenseType', 'step1') }">
                    <label class="control-label col-sm-4">
                        License Type <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Type of business license (e.g., cultivation, manufacturing, distribution, retail)'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" placeholder="Seeds clone, other multiple types etc." class="form-control"
                            name="license.licenseType" v-qa="'license.licenseType'" v-validate="{ required: true }"
                            data-vv-scope="step1" v-model="application.license.licenseType" />
                    </div>
                </div>

                <div v-if="isDomestic" class="mt-3 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                <span>Compliance</span>
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>


                <div class="form-group" :class="{ 'has-error': errors.has('description', 'step1') }">
                    <label class="control-label col-sm-4">
                        Description of your business <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Describe your business type, what products you sell, and your target market'"></i>
                    </label>
                    <div class="col-sm-8">
                        <textarea cols="20" rows="10"
                            placeholder="Include information on the type of business and what you will be buying or selling"
                            class="form-control" v-validate="'required'" v-qa="'description'" name="description"
                            data-vv-scope="step1" v-model="application.description">
                        </textarea>
                    </div>
                </div>
                <div v-if="isDomestic" class="form-group" :class="{ 'has-error': errors.has('monthlySales', 'step1') }">
                    <label class="control-label col-sm-4">
                        Your average combined monthly sales <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Select your estimated monthly sales volume to help us understand your business size'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <div class="form-control-select">
                            <select name="monthlySales" class="form-control" v-model="application.monthlySales"
                                v-validate="'required'" v-qa="'monthlySales'" data-vv-scope="step1">
                                <option value="" disabled>Please select</option>
                                <option value="$500-1000">$500-1000</option>
                                <option value="$1000-2000">$1000-2000</option>
                                <option value="$2000+">$2000+</option>
                            </select>
                            <span class="form-control-select-arrow">
                                <span class="caret"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <div v-if="isDomestic" class="form-group">
                    <label class="control-label col-sm-4">
                        Please upload proof of your domain (if online entity, a simple screenshot helps) or proof of
                        business
                        address if brick and mortar
                        store
                    </label>
                    <div class="col-sm-8">
                        <application-uploader-and-preview data-vv-name="proofOfAddress" v-qa="'proofOfAddress'"
                            v-model="application.proofOfAddress" />
                    </div>
                </div>
                <div v-if="isDomestic" class="form-group">
                    <label class="control-label col-sm-4">
                        Please upload your personal ID
                        <div class="font-weight-400 font-size-13">
                            ( e.g. unexpired driver license or government issued photo ID )
                        </div>
                    </label>
                    <div class="col-sm-8">
                        <application-uploader-and-preview data-vv-name="personalId" v-qa="'personalId'"
                            v-model="application.personalId" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-sm-4"> </div>
                    <div class="col-sm-8">
                        <div class="btn-group" role="group" aria-label="...">
                            <button type="submit" class="btn btn-info  w-25">Next</button>
                        </div>
                    </div>
                </div>
            </form>
        </section>
        <section v-show="step === 1">
            <form class="form" method="post" action="#" @submit.prevent="nextStep">
                <div class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                <span v-if="isDomestic">Legal Address</span>
                                <span v-else>Main Facility Address</span>
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('legalAddress.line1') }">
                    <label class="control-label col-sm-4">
                        Address Line 1 <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Street address, P.O. box, company name, c/o'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="legalAddress.line1"
                            v-model="application.legalAddress.line1" v-qa="'addressLine1'" v-validate="'required'"
                            data-vv-scope="step2" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-sm-4">
                        Address Line 2
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Apartment, suite, unit, building, floor, etc. (optional)'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="line2" v-qa="'addressLine2'"
                            v-model="application.legalAddress.line2" data-vv-scope="step2" />
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('legalAddress.city') }">
                    <label class="control-label col-sm-4">
                        City <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'City or town name'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="legalAddress.city"
                            v-model="application.legalAddress.city" v-qa="'addressCity'" v-validate="'required'"
                            data-vv-scope="step2" />
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('legalAddress.postal') }">
                    <label class="control-label col-sm-4">
                        Postal Code/ZIP Code <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'ZIP code for US addresses or postal code for international addresses'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="legalAddress.postal"
                            v-model="application.legalAddress.postal" v-qa="'addressPostal'" v-validate="'required'"
                            data-vv-scope="step2" />
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('legalAddress.region') }">
                    <label class="control-label col-sm-4">
                        {{ cityRegionProvinceLabel }}
                    </label>
                    <div v-if="isDomestic" class="col-sm-8 col-md-4">
                        <state-selector v-model="application.legalAddress.region" :states="states" data-vv-scope="step2" />
                    </div>
                    <div v-else class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="legalAddress.region"
                            v-model="application.legalAddress.region" v-qa="'addressIState'" v-validate="'required'"
                            data-vv-scope="step2" />
                    </div>
                </div>
                <div v-if="!isDomestic" class="form-group">
                    <label class="control-label col-sm-4">
                        Country
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input type="'text" class="form-control" :value="state.name" disabled />
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-sm-4"> </div>
                    <div class="col-sm-8">
                        <div class="btn-group" role="group" aria-label="...">
                            <button type="submit" class="btn btn-default w-25" @click.prevent="prevStep()">Previous</button>
                            <button type="submit" class="btn btn-info  w-25">Next</button>
                        </div>
                    </div>
                </div>
            </form>
        </section>
        <section v-show="step === 2">
            <form class="form" method="post" action="#" @submit.prevent="nextStep">

                <div class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                Primary Contact
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('fullName', 'step3') }">
                    <label class="control-label col-sm-4">
                        Full Name <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Full legal name of the primary contact person'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="text" class="form-control" name="fullName" v-model="application.fullName"
                            v-qa="'fullName'" v-validate.lazy="'required'" data-vv-scope="step3" />
                    </div>
                </div>
                <div class="form-group" :class="{ 'has-error': errors.has('phone', 'step3') }">
                    <label class="control-label col-sm-4">
                        Phone Number <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'Primary contact phone number (format: (*************)'"></i>
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <input-error type="tel" class="form-control" name="phone" v-model="formattedPhone" v-qa="'phone'"
                            v-validate.lazy="'required|min:14'" data-vv-scope="step3"
                            @input="formatPhoneNumber" placeholder="(*************" />
                    </div>
                </div>

                <div class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                License
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label class="control-label col-sm-4">
                        I am applying to sell THC products or Flower derived CBD products
                    </label>
                    <div class="col-sm-8 col-md-4">
                        <div class="switch-checkbox switch-success"
                            @click="application.license.cannabis = !application.license.cannabis" v-qa="'license'"
                            :class="{ 'switch-checkbox-on': application.license.cannabis, 'switch-checkbox-off': !application.license.cannabis }">
                            <div
                                :class="{ 'switch-checkbox-label-on': application.license.cannabis, 'switch-checkbox-label-off': !application.license.cannabis }">
                                <span class="switch-checkbox-label">
                                    {{ application.license.cannabis ? 'Yes' : 'No' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <template v-if="application.license.cannabis && isDomestic">

                    <!--START VV ADDED-->

                    <div class="form-group" v-if="isDomestic" :class="{ 'has-error': errors.has('license.state', 'step3') }">
                        <label class="control-label col-sm-4">
                            Licensing State <span class="text-danger">*</span>
                            <i class="fa fa-question-circle text-muted ml-1"
                               v-tooltip="'State where your cannabis license was issued'"></i>
                        </label>
                        <div class="col-sm-8 col-md-4">
                            <div class="form-control-select">
                                <regulatory-state-selector v-model="application.license.state" :states="states"
                                    data-vv-scope="step3" name="license.state" v-validate="'required'" />
                            </div>
                        </div>
                    </div>

                    <!--div class="form-group">
                <label class="control-label col-sm-4">
                    Licensing Regulator
                </label>
                <div class="col-sm-8 col-md-4">
                    <div class="form-control-select">
                        <regulatory-body-selector v-model="application.license.regulator" :states="states"/>
                    </div>
                </div>
            </div-->

                    <!--STOP VV ADDED-->


                    <div class="form-group" v-if="isDomestic" :class="{ 'has-error': errors.has('license.number', 'step3') }">
                        <label class="control-label col-sm-4">
                            License Number <span class="text-danger">*</span>
                            <i class="fa fa-question-circle text-muted ml-1"
                               v-tooltip="'Your cannabis business license number'"></i>
                        </label>
                        <div class="col-sm-8 col-md-4">
                            <input-error type="text" class="form-control" v-model="application.license.number"
                                v-qa="'licenseNumber'" data-vv-scope="step3" name="license.number" v-validate="'required'" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-4">
                            Licensee Name
                        </label>
                        <div class="col-sm-8 col-md-4">
                            <input-error type="text" class="form-control" v-model="application.license.name"
                                v-qa="'licenseName'" data-vv-scope="step3" />
                        </div>
                    </div>
                    <div class="form-group" :class="{ 'has-error': errors.has('license.licenseType', 'step3') }">
                        <label class="control-label col-sm-4">
                            License Types <span class="text-danger">*</span>
                            <i class="fa fa-question-circle text-muted ml-1"
                               v-tooltip="'Type of cannabis license (e.g., cultivation, manufacturing, distribution)'"></i>
                        </label>
                        <div class="col-sm-8 col-md-4">
                            <input-error type="text" class="form-control" v-model="application.license.licenseType"
                                v-qa="'licenseType'" data-vv-scope="step3" name="license.licenseType" v-validate="'required'" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-4">
                            Business Name
                        </label>
                        <div class="col-sm-8 col-md-4">
                            <input-error type="text" class="form-control" v-model="application.license.businessName"
                                v-qa="'licenseBusinessName'" data-vv-scope="step3" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-4">
                            County/Region
                        </label>
                        <div class="col-sm-8 col-md-4">
                            <input-error type="text" class="form-control" v-model="application.license.county"
                                v-qa="'licenseCounty'" data-vv-scope="step3" />
                        </div>
                    </div>

                </template>

                <div class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                Supporting Documents
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-sm-4">
                        {{ incorporationSupportingDocsLabel }}
                    </label>
                    <div class="col-sm-8">
                        <div class="d-flex align-items-center flex-wrap m-minus-1">
                            <div class="m-1" v-for="file of application.files" :key="file.uuid">
                                <document-preview :file="file" @removeFile="removeFile(file)" can-remove
                                    v-if="!file.docName" />
                            </div>
                            <div class="m-1">
                                <uploader :custom-button="true" @file="addFile" :images-only="false" multiple></uploader>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="requiredDocuments.length > 0" class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                Required Documents
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>
                <div v-if="requiredDocuments.length > 0">
                    <div v-for="(doc, index) in requiredDocuments" :key="index">
                        <div class="form-group">
                            <div class="flex-grow-1">
                                <hr class="mt-0 mb-0 hr-gray-lighter" style="height: 10pt; width: 50vw;">
                            </div>
                            <label class="control-label col-sm-2">{{ doc.name }}</label>
                            <label class="col-sm-2">{{ doc.description }}</label>
                            <br />
                            <div class="col-sm-2">
                                <div class="m-1" v-for="file in filteredFiles" :key="file.uuid">
                                    <document-preview :file="file" @removeFile="removeFile(file)" can-remove />
                                </div>
                                <span>Must be an image and not larger than 10MB</span>
                                <uploader :custom-button="true" @file="addFileWithExpiration($event, doc)"
                                    :images-only="false" multiple />
                            </div>
                            <div v-if="doc.expiration" class="col-sm-6"
                                :class="{ 'has-error': errors.has(`expirationDate for ${doc.name}`, 'step3') }">
                                <label class="control-label">
                                    Expiration Date
                                </label>
                                <br />
                                <date-picker class="form-control p-0"
                                    :class="{ 'has-error': errors.has(`expirationDate${index}`, 'step3') }"
                                    v-model="expirationDates[doc._id]" :min="minApplicationExpiredDate"
                                    :v-qa="`expirationDate${index}`" :name="`expirationDate for ${doc.name}`"
                                    data-vv-scope="step3" v-validate="'required'" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="pr-3">
                            <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                                Terms &amp; Conditions
                            </h5>
                        </div>
                        <div class="flex-grow-1">
                            <hr class="mt-0 mb-0 hr-gray-lighter">
                        </div>
                    </div>
                </div>

                <div class="form-group" :class="{ 'has-error': errors.has('agreeTerms', 'step3') }">
                    <label class="control-label col-sm-4">
                        Terms and Conditions <span class="text-danger">*</span>
                        <i class="fa fa-question-circle text-muted ml-1"
                           v-tooltip="'You must agree to all terms and conditions to complete your application'"></i>
                    </label>
                    <div class="col-sm-8">
                        <terms-and-conditions v-model="application.agreeTerms" :isDomestic="isDomestic" />
                        <input type="hidden" name="agreeTerms" v-model="application.agreeTerms"
                               v-validate="'required'" data-vv-scope="step3" />
                        <div v-if="errors.has('agreeTerms', 'step3')" class="text-danger mt-1">
                            <small>You must agree to all terms and conditions to proceed.</small>
                        </div>
                    </div>
                </div>
            </form>
        </section>

        <hr class="my-6 hr-gray-7">

        <!-- Product Import Section -->
        <section v-show="step === 2">
            <div class="mt-6 mb-3">
                <div class="d-flex align-items-center">
                    <div class="pr-3">
                        <h5 class="mt-0 mb-0 text-uppercase text-brand-primary">
                            Product Import (Optional)
                        </h5>
                    </div>
                    <div class="flex-grow-1">
                        <hr class="mt-0 mb-0 hr-gray-lighter">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-12">
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="mr-3">
                                <svg class="icon icon-lg" viewBox="0 0 24 24">
                                    <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z" fill-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p>You can upload your product inventory now or do it later after your application is approved.</p>
                                <p class="mb-0">Products will remain inactive until your application is verified.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-12">
                    <div class="d-flex justify-content-center mb-4">
                        <button type="button" class="btn btn-outline-info mr-3" @click="downloadProductTemplate">
                            <svg class="icon mr-1" viewBox="0 0 24 24">
                                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                            </svg>
                            Download Template
                        </button>
                        <button type="button" class="btn btn-outline-info" @click="showImportModal = true">
                            <svg class="icon mr-1" viewBox="0 0 24 24">
                                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                            </svg>
                            Import Products
                        </button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-12 text-center">
                    <button type="button" class="btn btn-info" @click="nextStep">
                        Continue to Next Step
                    </button>
                </div>
            </div>
        </section>

        <!-- Error Summary Component -->
        <div v-if="showErrorSummary && missingRequiredFields.length > 0" v-show="step === 3" class="form-group">
            <div class="col-sm-12">
                <div class="max-w-300 mx-auto">
                    <div class="alert alert-danger">
                        <h5><i class="fa fa-exclamation-triangle"></i> Missing Required Fields</h5>
                        <p>Please complete the following required fields before submitting your application:</p>
                        <ul class="mb-2">
                            <li v-for="field in missingRequiredFields" :key="field" class="mb-1">
                                <a href="#" @click.prevent="jumpToField(field)" class="text-danger">
                                    {{ field }}
                                </a>
                            </li>
                        </ul>
                        <button @click="showErrorSummary = false" class="btn btn-sm btn-outline-danger">
                            Dismiss
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div v-show="step === 3" class="form-group">
            <div class="col-sm-12">
                <div class="max-w-300 mx-auto">
                    <!-- Show validation errors if submit is disabled -->
                    <div v-if="saveDisabled && missingRequiredFields.length > 0" class="alert alert-warning mb-3">
                        <small>
                            <strong>Complete required fields to submit:</strong>
                            <span v-for="(field, index) in missingRequiredFields.slice(0, 3)" :key="field">
                                <a href="#" @click.prevent="jumpToField(field)" class="text-warning">{{ field }}</a><span v-if="index < Math.min(missingRequiredFields.length, 3) - 1">, </span>
                            </span>
                            <span v-if="missingRequiredFields.length > 3">
                                and {{ missingRequiredFields.length - 3 }} more...
                            </span>
                        </small>
                    </div>

                    <spinner-button v-if="status === 'Draft'" type="button" class="btn btn-info btn-block btn-lg"
                        v-qa="'finish-btn'" skip-saved :source="$parent" :disabled="saveDisabled" @click="finish(false)">
                        Finish the application
                    </spinner-button>
                    <template v-else>
                        <spinner-button type="submit" class="btn btn-info btn-block btn-lg" v-qa="'submit-btn'" skip-saved
                            :source="$parent" :disabled="saveDisabled">
                            Submit
                        </spinner-button>
                        <spinner-button type="button" class="btn btn-warning btn-block btn-lg" v-qa="'save-btn'" skip-saved
                            :source="$parent" eventName="isSaving" @click="send(true)">
                            Save and complete later
                        </spinner-button>
                    </template>
                </div>
            </div>
        </div>

        <!-- Import Products Modal -->
        <modal v-if="showImportModal" @close="showImportModal = false">
            <product-import-modal @close="showImportModal = false" @import-complete="importComplete"></product-import-modal>
        </modal>
    </form>
</template>

<script>
import DocumentPreview from '@/common/components/DocumentPreview';
import SpinnerButton from '@/common/components/SpinnerButton';
import StateSelector from '@/common/components/StateSelector';
import RegulatoryStateSelector from '@/common/components/RegulatoryStateSelector';
// import RegulatoryBodySelector from '@/common/components/RegulatoryBodySelector';
import TermsAndConditions from '@/vendor/components/TermsAndConditions';
import ApplicationUploaderAndPreview from '@/common/components/ApplicationUploaderAndPreview';
import { DateTime } from 'luxon';
import DatePicker from '@/common/components/Picker';
import Modal from '@/common/plugins/modal/Modal.vue';
import ProductImportModal from '@/vendor/components/product/ProductImportModal';
import { saveAs } from 'file-saver';



export default {
    name: 'ApplicationForm',
    components: {
        ApplicationUploaderAndPreview,
        DatePicker,
        StateSelector,
        RegulatoryStateSelector,
        // RegulatoryBodySelector,
        TermsAndConditions,
        DocumentPreview,
        SpinnerButton,
        Modal,
        ProductImportModal
    },
    inject: ['$productService'],
    props: {
        application: {
            type: Object
        },
        credentials: {
            type: Object
        },
        states: {
            type: Array,
            default: () => []
        },
        stateP: {
            type: Object
        },
        status: {
            type: String
        }
    },
    data() {
        return {
            state: this.stateP,
            expirationDates: {},
            file: null,
            step: this.getInitialStep(),
            showImportModal: false,
            steps: [
                { title: 'Business', validated: true },
                { title: 'Address', validated: false },
                { title: 'Products', validated: false },
                { title: 'Documents', validated: false }
            ],
            showErrorSummary: false
        };
    },
    watch: {
        'application.files'() {
            if (Object.keys(this.application).includes('files')) {
                if (this.application['files'] && this.application['files'].length > 0) {
                    this.application.files.forEach(file => {
                        this.expirationDates[file.docName] = file.expirationDate.toLocaleString();
                    });
                }
            }
        }
    },
    computed: {
        currentStep() {
            return this.steps[this.step];
        },
        formattedPhone: {
            get() {
                return this.formatPhoneDisplay(this.application.phone);
            },
            set(value) {
                // Store only digits in the application data
                this.application.phone = value.replace(/\D/g, '');
            }
        },
        saveDisabled() {
            // Check if terms are agreed
            if (!this.application.agreeTerms) {
                return true;
            }

            // If user is on the final step (step 3), check if they have all required fields
            // instead of relying on step validation flags which might be stale
            if (this.step === 3) {
                const missing = this.missingRequiredFields;
                return missing.length > 0;
            }

            // For other steps, check if all previous steps are validated
            const allPreviousStepsValidated = this.steps.slice(0, this.step + 1).every(step => step.validated);
            if (!allPreviousStepsValidated) {
                return true;
            }

            return false;
        },
        missingRequiredFields() {
            const missing = [];

            // Step 1: Business information
            if (!this.application.businessName) missing.push('Business Name');
            if (!this.application.description) missing.push('Business Description');
            if (!this.application.monthlySales) missing.push('Monthly Sales');

            if (this.isDomestic) {
                if (!this.application.tradeName) missing.push('Trade Name');
                if (!this.application.ein) missing.push('EIN/Tax ID');
            }

            // License fields - ALWAYS REQUIRED (backend schema requires them)
            if (!this.application.license.number) missing.push('License Number');
            if (!this.application.license.licenseType) missing.push('License Type');

            // Additional license fields for domestic users with cannabis enabled
            if (this.application.license.cannabis && this.isDomestic) {
                if (!this.application.license.state) missing.push('Licensing State');
            }

            // Step 2: Address information
            if (!this.application.legalAddress.line1) missing.push('Address Line 1');
            if (!this.application.legalAddress.city) missing.push('City');
            if (!this.application.legalAddress.postal) missing.push('Postal Code');
            if (!this.application.legalAddress.region) missing.push('State/Region');

            // Step 3: Contact information
            if (!this.application.fullName) missing.push('Full Name');
            if (!this.application.phone) missing.push('Phone Number');

            // Terms agreement
            if (!this.application.agreeTerms) missing.push('Terms Agreement');

            return missing;
        },
        isDomestic() {
            return this.state && this.state.isDomestic;
            // this.credentials.location === 'domestic';
        },
        cityRegionProvinceLabel() {
            return this.isDomestic ? 'State' : 'State/Region/Province';
        },
        incorporationSupportingDocsLabel() {
            if (this.state.name.toLowerCase().includes('canada')) {
                return 'Please upload supporting documents regarding company incorporation, hemp or cannabis license, Manifest that contains cannabis as medicinal or scientific purposes.';
            }
            else if (this.state.name.toLowerCase().includes('colombia')) {
                return 'Please upload supporting documents regarding company incorporation, License that includes the exportation modality, Registry as an exportation company, National cultivares registry, and quota given if the product contains THC';
            }
            else if (this.state.name.toLowerCase().includes('israel')) {
                return 'Please upload supporting documents regarding company incorporation, License to possess dangerous cannabis products by MCUMH, Either EU-GMP/CUMCS-GAP/ICAAN-GAP License, National cultivares registry';
            }
            return 'Please upload any supporting documents regarding company incorporation, and if you plan to buy or sell THC products, all applicable licenses';
        },
        filteredFiles() {
            return this.application.files.filter(file => file.docName);
        },
        // requiredDocuments() {
        //     return this.state.requiredDocuments || [];
        // },
        requiredDocuments() {
            return this.state.requiredDocuments.map(doc => ({
                ...doc,
                expiration: !!doc.expiration // Add the expiration property
            }));
        },
        minApplicationExpiredDate() {
            return DateTime.local().endOf('day')
                .toJSDate();
        },
        businessNameModel: {
            get() {
                return this.application && this.application.businessName
                    ? this.application.businessName || ''
                    : this.credentials
                        ? this.credentials.businessName
                        : '';
            },
            set(value) {
                this.application.businessName = value;
            }
        }
    },
    methods: {
        getInitialStep() {
            // If we're editing an existing application, start at the final step
            // so users can review and submit without going through all steps again
            if (this.status === 'edit' || (this.application && this.application.status === 'Pending')) {
                return 3; // Final step
            }
            // For new applications, start at step 0
            return 0;
        },
        async send(isDraft = false) {
            if (isDraft) {
                // Went this route rather than validating because it is just a draft and backend model requires a name and phone or will give an error.
                if (!this.application.fullName) {
                    this.$flashError(`We need your Full Name before we save this draft!`);
                    return
                }
                if (!this.application.phone) {
                    this.$flashError(`We need your Phone before we save this draft!`);
                    return
                }
                this.$emit('send', isDraft);
            } else {
                const isValidated = await this.validateStep(this.steps.length - 1);

                if (isValidated) {
                    const isFilesUplaoded = this.checkFilesUploaded();
                    if (isFilesUplaoded) {
                        this.$emit('send', isDraft);
                    }
                    return;
                } else {
                    let missingFields = [];
                    this.$validator.errors.items.forEach(item => {
                        let fieldname = item.field.charAt(0).toUpperCase() + item.field.slice(1);
                        missingFields.push(fieldname);
                    })
                    this.$flashError(`Missing field(s): ${missingFields.join(", ")}`);
                }
            }
        },
        async finish(forDraft) {
            if (forDraft) {
                this.$emit('finish', forDraft);
            } else {
                // Check if all required fields are filled
                const missing = this.missingRequiredFields;
                if (missing.length > 0) {
                    this.showErrorSummary = true;
                    this.$flashError(`Please complete all required fields before submitting your application.`);
                    return;
                }

                const isValidated = await this.validateStep(this.steps.length - 1);

                if (isValidated) {
                    const isFilesUplaoded = this.checkFilesUploaded();
                    if (isFilesUplaoded) {
                        this.$emit('finish', forDraft);
                    }
                    return;
                } else {
                    let missingFields = [];
                    this.$validator.errors.items.forEach(item => {
                        let fieldname = item.field.charAt(0).toUpperCase() + item.field.slice(1);
                        missingFields.push(fieldname);
                    })
                    this.$flashError(`Missing field(s): ${missingFields.join(", ")}`);
                }
            }
        },
        jumpToField(fieldName) {
            // Map field names to their corresponding steps and actions
            const fieldStepMap = {
                'Business Name': { step: 0, action: () => this.goToStep(0) },
                'Trade Name': { step: 0, action: () => this.goToStep(0) },
                'EIN/Tax ID': { step: 0, action: () => this.goToStep(0) },
                'License Number': { step: 0, action: () => this.goToStep(0) },
                'License Type': { step: 0, action: () => this.goToStep(0) },
                'Business Description': { step: 0, action: () => this.goToStep(0) },
                'Monthly Sales': { step: 0, action: () => this.goToStep(0) },
                'Address Line 1': { step: 1, action: () => this.goToStep(1) },
                'City': { step: 1, action: () => this.goToStep(1) },
                'Postal Code': { step: 1, action: () => this.goToStep(1) },
                'State/Region': { step: 1, action: () => this.goToStep(1) },
                'Full Name': { step: 2, action: () => this.goToStep(2) },
                'Phone Number': { step: 2, action: () => this.goToStep(2) },
                'Licensing State': { step: 2, action: () => this.goToStep(2) },
                'Cannabis License Number': { step: 2, action: () => this.goToStep(2) },
                'Cannabis License Type': { step: 2, action: () => this.goToStep(2) },
                'Terms Agreement': { step: 2, action: () => this.goToStep(2) }
            };

            const fieldInfo = fieldStepMap[fieldName];
            if (fieldInfo) {
                fieldInfo.action();
                this.showErrorSummary = false;
            }
        },
        async validateStep(stepIndex) {
            let result = false;
            let scope = `step${stepIndex + 1}`;

            result = await this.$validator.validateAll(scope);

            // Additional validation for specific steps
            if (stepIndex === 0) {
                // Step 1: Business information
                result = result && this.application.businessName &&
                         this.application.description && this.application.monthlySales;

                if (this.isDomestic) {
                    result = result && this.application.tradeName && this.application.ein;
                }

                // License fields are ALWAYS required (backend schema requirement)
                result = result && this.application.license.number && this.application.license.licenseType;
            } else if (stepIndex === 1) {
                // Step 2: Address information
                result = result && this.application.legalAddress.line1 &&
                         this.application.legalAddress.city &&
                         this.application.legalAddress.postal &&
                         this.application.legalAddress.region;
            } else if (stepIndex === 2) {
                // Step 3: Contact information AND terms agreement (both are in the same step section)
                // Note: Product import is optional and doesn't affect validation
                const contactValid = this.application.fullName && this.application.phone;
                const termsValid = this.application.agreeTerms;

                // Check additional license fields if cannabis is enabled for domestic users
                let additionalLicenseValid = true;
                if (this.application.license.cannabis && this.isDomestic) {
                    additionalLicenseValid = this.application.license.state;
                }

                result = result && contactValid && termsValid && additionalLicenseValid;
            }

            this.steps[stepIndex].validated = result;
            return result;
        },
        setStep(newStep) {
            this.step = newStep;
        },
        checkFilesUploaded() {
            let missingFiles = [];

            // loop through required documents
            for (let reqDoc of this.requiredDocuments) {
                let filesUploaded = 0;

                // loop through uploaded files to check if required files are uploaded
                for (let file of this.application.files) {
                    if (reqDoc._id === file.docName) {
                        filesUploaded++;
                        file.expirationDate = this.expirationDates[reqDoc._id] && new Date(this.expirationDates[reqDoc._id]);
                    }
                }

                // check if required files are uploaded
                if (filesUploaded === 0) {
                    missingFiles.push(reqDoc.name);
                }
            }

            // Documents are now optional for application completion, but show warning
            if (missingFiles.length > 0) {
                this.$flashWarning(`Warning: Missing company documents: ${missingFiles.join(', ')}. Your products won't be visible until these documents are uploaded.`);
            }

            // Always return true to allow application completion
            return true;

        },
        addFile(file) {
            this.application.files.push(file);
        },
        removeFile(file) {
            this.application.files = this.application.files.filter(image => image.uuid !== file.uuid);
        },
        addFileWithExpiration(file, doc) {
            let uploadFile = {};
            let docName = doc._id;
            if (doc.expiration) {
                const expirationDate = this.expirationDates[doc._id] && new Date(this.expirationDates[doc._id]);
                // if (!expirationDate) {
                //     this.$flashError('This document requires an expiration date. Please provide the expiration date before uploading the file');
                //     return;
                // }
                // if (expirationDate <= new Date()) {
                //     return this.$flashError('Document is expired!');
                // }
                uploadFile = { ...file, expirationDate, docName };
            }
            else {
                uploadFile = { ...file, docName };
            }
            this.addFile(uploadFile);
        },
        prevStep() {
            if (this.step > 0) {
                this.step--;
            }
        },
        async nextStep() {
            const isValidated = await this.validateStep(this.step);

            if (isValidated) {
                // Auto-save progress after each step
                // Temporarily disabled to debug finish application issue
                // try {
                //     await this.autoSaveProgress();
                // } catch (error) {
                //     console.warn('Auto-save failed:', error);
                //     // Don't block progression if auto-save fails
                // }
                this.step++;
            }
        },
        async autoSaveProgress() {
            // Auto-save the application as draft to track user progress
            if (this.application && this.application.businessName) {
                try {
                    // Emit save event with draft flag
                    this.$emit('send', true); // true = isDraft
                } catch (error) {
                    console.warn('Auto-save progress failed:', error);
                }
            }
        },
        async goToStep(index) {
            // Allow navigation to current step or previous steps
            if (index <= this.step) {
                this.step = index;
                return;
            }

            // For forward navigation, validate all previous steps
            if (this.canNavigateToStep(index)) {
                this.step = index;
            } else {
                // Show error message about incomplete steps
                const incompleteSteps = [];
                for (let i = 0; i < index; i++) {
                    if (!this.steps[i].validated) {
                        incompleteSteps.push(this.steps[i].title);
                    }
                }
                this.$flashError(`Please complete the following steps first: ${incompleteSteps.join(', ')}`);
            }
        },
        canNavigateToStep(index) {
            // Can always navigate to current step or backwards
            if (index <= this.step) {
                return true;
            }

            // For forward navigation, all previous steps must be validated
            for (let i = 0; i < index; i++) {
                if (!this.steps[i].validated) {
                    return false;
                }
            }
            return true;
        },
        canProceedToNextStep() {
            return this.step === 0 || this.steps[this.step - 1].validated;
        },
        getStepTooltip(stepObj, index) {
            if (!stepObj.validated && index < this.step) {
                return `${stepObj.title} - Incomplete (click to fix)`;
            } else if (!this.canNavigateToStep(index)) {
                return `${stepObj.title} - Complete previous steps first`;
            } else if (stepObj.validated) {
                return `${stepObj.title} - Complete`;
            }
            return stepObj.title;
        },
        downloadProductTemplate(format = 'xlsx') {
            // Use the product service to download the template
            this.$productService.getImportTemplate(format)
                .then(response => {
                    const blob = new Blob([response], { type: response.type });
                    const filename = `product_import_template.${format}`;
                    saveAs(blob, filename);
                })
                .catch(error => {
                    this.$flashError('Error downloading template: ' + error.message);
                });
        },
        importComplete() {
            this.showImportModal = false;
            this.$flashSuccess('Products imported successfully! They will remain inactive until your application is approved.');
        },
        // Helper method to validate step if it has been visited
        async validateStepIfActive(stepIndex) {
            if (stepIndex <= this.step) {
                await this.validateStep(stepIndex);
            }
        },
        async revalidateAllSteps() {
            // Re-validate all steps to ensure proper state
            for (let i = 0; i < this.steps.length; i++) {
                await this.validateStep(i);
            }
        },
        // Debug method - can be called from browser console
        async debugValidation() {
            console.log('=== DEBUG VALIDATION ===');
            console.log('Current step:', this.step);
            console.log('Steps validation state:', this.steps.map((s, i) => ({ step: i, title: s.title, validated: s.validated })));
            console.log('Missing required fields:', this.missingRequiredFields);
            console.log('Save disabled:', this.saveDisabled);
            console.log('Terms agreed:', this.application.agreeTerms);

            console.log('Re-validating all steps...');
            await this.revalidateAllSteps();
            console.log('After revalidation:', this.steps.map((s, i) => ({ step: i, title: s.title, validated: s.validated })));
            console.log('Save disabled after revalidation:', this.saveDisabled);
        },
        // Phone number formatting methods
        formatPhoneDisplay(phone) {
            if (!phone) return '';

            // Remove all non-digits
            const cleaned = phone.replace(/\D/g, '');

            // Format as (XXX) XXX-XXXX
            if (cleaned.length >= 10) {
                return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
            } else if (cleaned.length >= 6) {
                return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
            } else if (cleaned.length >= 3) {
                return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
            } else {
                return cleaned;
            }
        },
        formatPhoneNumber(event) {
            const input = event.target;
            const value = input.value;

            // Remove all non-digits
            const cleaned = value.replace(/\D/g, '');

            // Limit to 10 digits
            const limited = cleaned.slice(0, 10);

            // Format and set the value
            const formatted = this.formatPhoneDisplay(limited);
            input.value = formatted;

            // Update the model
            this.application.phone = limited;

            // Trigger validation
            this.validateStepIfActive(2);
        }
    },
    watch: {
        // Watch for changes in application data and revalidate steps
        'application.businessName': function() {
            this.validateStepIfActive(0);
        },
        'application.tradeName': function() {
            this.validateStepIfActive(0);
        },
        'application.ein': function() {
            this.validateStepIfActive(0);
        },
        'application.license.number': function() {
            this.validateStepIfActive(0);
        },
        'application.license.licenseType': function() {
            this.validateStepIfActive(0);
        },
        'application.description': function() {
            this.validateStepIfActive(0);
        },
        'application.monthlySales': function() {
            this.validateStepIfActive(0);
        },
        'application.legalAddress.line1': function() {
            this.validateStepIfActive(1);
        },
        'application.legalAddress.city': function() {
            this.validateStepIfActive(1);
        },
        'application.legalAddress.postal': function() {
            this.validateStepIfActive(1);
        },
        'application.legalAddress.region': function() {
            this.validateStepIfActive(1);
        },
        'application.fullName': function() {
            this.validateStepIfActive(2);
        },
        'application.phone': function() {
            this.validateStepIfActive(2);
        },
        'application.agreeTerms': function() {
            this.validateStepIfActive(2);
        },
        'application.license.cannabis': function() {
            this.validateStepIfActive(2);
        },
        'application.license.state': function() {
            this.validateStepIfActive(2);
        },
        'application.license.number': function() {
            this.validateStepIfActive(2);
        },
        'application.license.licenseType': function() {
            this.validateStepIfActive(2);
        }
    },
    async created() {
        if (!this.application) {
            this.application = {};
        }

        // Clear out the state for the country information instead.
        if (!this.isDomestic) {
            this.application.legalAddress.region = ''
        }
        this.application.businessName = this.credentials
            ? this.credentials.businessName : this.application.businessName || '';
        // Check if credentials.location is 'international' and has a value
        if (this.state && !this.state.isDomestic) {
            // Set the application.license.county to your desired value
            this.application.license.county = this.state.name;
        }
        else if (!this.state && this.credentials && this.credentials.state) {
            this.state = this.states.find(st => st.id === this.credentials.state);
        }

        // Auto-validate all steps on load to ensure proper state
        await this.$nextTick(); // Wait for DOM to be ready
        for (let i = 0; i < this.steps.length; i++) {
            await this.validateStep(i);
        }
    }
};
</script>
