<template>
    <div>
        <div>
            <div class="checkbox-default" :class="{ 'text-danger': !agreeTermsOfServiceAndPrivacyPolicy }">
                <label>
                    <input type="checkbox"
                           v-model="agreeTermsOfServiceAndPrivacyPolicy"
                           v-qa="'termsOfService'"
                           @change="handler"/>
                    <span class="text-danger">*</span> I agree to CanIDeal
                    <a href="https://docs.google.com/document/d/1GvQJk33pehu9BU-BCVCpDJ_mDV0ioJZoG0mp32WLsbA/export?format=pdf" target="_blank">Term of Service</a> and
                    <a href="https://docs.google.com/document/d/1G_cWSzV7ZQMNiFuIN2n1CYtnWMdyq0IyGHw0GwyIiD0/export?format=pdf" target="_blank">Privacy Policy</a>
                </label>
            </div>
        </div>
        <div>
            <div class="checkbox-default" :class="{ 'text-danger': !agreeUserFeesAndPaymentPolicy }">
                <label>
                    <input type="checkbox"
                           v-model="agreeUserFeesAndPaymentPolicy"
                           v-qa="'userFeesAndPaymentPolicy'"
                           @change="handler"/>
                    <span class="text-danger">*</span> I agree to CanIDeal <a href="https://docs.google.com/document/d/1lkL2WE9KSG6VFz05KyULq8zTmNbd2IKwqzKLq2PkWgg/export?format=pdf" target="_blank">User Fees and Payment Policy</a>
                </label>
            </div>
        </div>
        <div v-if="isDomestic">
            <div class="checkbox-default" :class="{ 'text-danger': !agreeReturnPolicy }">
                <label>
                    <input type="checkbox"
                           v-model="agreeReturnPolicy"
                           v-qa="'returnPolicy'"
                           @change="handler"/>
                    <span class="text-danger">*</span> I agree to CanIDeal <a href="https://docs.google.com/document/d/1SoIxbn0ugMy8XHNIBhny-3xAQEOH8bWSBy23Pr0aWfU/export?format=pdf" target="_blank">Return Policy</a>
                </label>
            </div>
        </div>

    </div>
</template>

<script>
export default {
    name: 'TermsAndConditions',
    model: {
        event: 'change',
        prop: 'value'
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        isDomestic: {
            type: Boolean
        }
    },
    data() {
        return {
            agreeTermsOfServiceAndPrivacyPolicy: false,
            agreeUserFeesAndPaymentPolicy: false,
            agreeReturnPolicy: false
        };
    },
    watch: {
        value: {
            immediate: true,
            handler(newValue) {
                // If the parent sets agreeTerms to true, we assume all terms were previously agreed
                // This handles cases where the form is being edited or loaded from saved data
                if (newValue && !this.allAgreed) {
                    this.agreeTermsOfServiceAndPrivacyPolicy = true;
                    this.agreeUserFeesAndPaymentPolicy = true;
                    this.agreeReturnPolicy = this.isDomestic ? true : false;
                }
            }
        }
    },
    computed: {
        allAgreed() {
            return this.agreeTermsOfServiceAndPrivacyPolicy
                && this.agreeUserFeesAndPaymentPolicy
                && (this.agreeReturnPolicy || !this.isDomestic);
        }
    },
    methods: {
        handler() {
            this.$nextTick(() => {
                const allAgreed = this.allAgreed;
                this.$emit('change', allAgreed);
            });
        }
    }
};
</script>
