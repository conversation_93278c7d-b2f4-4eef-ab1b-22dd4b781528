<template>
    <div>
        <div>
            <div class="row">
                <div class="col-lg-10 col-lg-offset-1">
                    <div class="d-flex d-xs-block align-items-center mb-6">
                        <div>
                            <h3 class="text-300 my-0 text-size-32">
                                Ticket {{$route.params.key}}
                            </h3>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-2">
                            <div class="mb-3">
                                <router-link to="/tickets">
                                    <span class="d-flex">
                                        <span>
                                            <svg class="icon" viewBox="0 0 24 24">
                                                <path d="M15.41 16.09L10.83 11.5 15.41 6.91 14 5.5 8 11.5 14 17.5Z"
                                                      fill-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                        <span>
                                            Back to Tickets
                                        </span>
                                    </span>
                                </router-link>
                            </div>
                        </div>
                        <div class="col-md-8 col-sm-12" v-if="ticket">

                            <div class="bg-white border-radius-3 mb-6">
                                <div class="table-responsive">
                                    <table class="table mb-0">
                                        <tbody>
                                        <tr>
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Ticket ID
                                            </th>
                                            <td class="text-gray-dark">
                                                {{ticket.key}}
                                            </td>
                                        </tr>
                                        <tr v-if="ticket.productOrder">
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Order ID
                                            </th>
                                            <td class="text-gray-dark">
                                                {{ticket.productOrder.key}}
                                            </td>
                                        </tr>
                                        <tr v-if="ticket.category">
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Category
                                            </th>
                                            <td class="text-gray-dark">
                                                <template v-if="ticket.category === 'account_issues'">Account Issues</template>
                                                <template v-else-if="ticket.category === 'login_problems'">Login Problems</template>
                                                <template v-else-if="ticket.category === 'product_management'">Product Management</template>
                                                <template v-else-if="ticket.category === 'payment_issues'">Payment Issues</template>
                                                <template v-else-if="ticket.category === 'general_support'">General Support</template>
                                                <template v-else-if="ticket.category === 'order_related'">Order Related</template>
                                                <template v-else>{{ticket.category}}</template>
                                            </td>
                                        </tr>
                                        <tr v-if="ticket.retailer">
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Retailer Name
                                            </th>
                                            <td class="text-gray-dark">
                                                {{ticket.retailer.businessName}}
                                            </td>
                                        </tr>
                                        <tr v-if="ticket.product">
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Product
                                            </th>
                                            <td class="text-gray-dark">
                                                {{ticket.product.name}}
                                            </td>
                                        </tr>
                                        <tr v-if="ticket.productOrder">
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Delivery Address
                                            </th>
                                            <td class="text-gray-dark">
                                                {{ticket.productOrder.deliveryAddress}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Subject
                                            </th>
                                            <td class="text-gray-dark">
                                                {{ticket.subject}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Status
                                            </th>
                                            <td class="text-nowrap"
                                                :class="{'text-brand-warning': ticket.status === 'opened','opacity-60': ticket.status === 'closed'}">
                                                <svg class="icon" viewBox="0 0 24 24">
                                                    <path d="M17.5 12c0 3.033-2.467 5.5-5.5 5.5A5.506 5.506 0 0 1 6.5 12c0-3.033 2.467-5.5 5.5-5.5s5.5 2.467 5.5 5.5z"
                                                          fill-rule="evenodd"></path>
                                                </svg>
                                                <template v-if="ticket.status === 'opened'">
                                                    Open
                                                </template>
                                                <template v-else-if="ticket.status === 'closed'">
                                                    Closed
                                                </template>
                                                <template v-else>
                                                    {{ticket.status}}
                                                </template>
                                            </td>
                                        </tr>

                                        <tr>
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Created At
                                            </th>
                                            <td class="text-gray-dark">
                                                <div class="text-nowrap">{{ticket.createdAt | date}}</div>
                                                <div class="text-size-13 opacity-60">
                                                    {{ticket.createdAt | time}}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Updated At
                                            </th>
                                            <td class="text-gray-dark">
                                                <div class="text-nowrap">{{ticket.updatedAt | date}}</div>
                                                <div class="text-size-13 opacity-60">
                                                    {{ticket.updatedAt | time}}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="bg-gray-2 text-400 opacity-60">
                                                Closed At
                                            </th>
                                            <td class="text-gray-dark">
                                                <template v-if="ticket.closedAt">
                                                    <div class="text-nowrap">{{ticket.closedAt | date}}</div>
                                                    <div class="text-size-13 opacity-60">
                                                        {{ticket.closedAt | time}}
                                                    </div>
                                                </template>
                                                <template v-else>–</template>
                                            </td>
                                        </tr>

                                        </tbody>
                                    </table>
                                </div>

                            </div>
                            <div>
                                <confirm v-if="ticket.status === 'opened'" title="Close Ticket">
                                    <template slot="text">
                                        Please confirm to close this ticket.
                                    </template>
                                    <button slot="confirm" class="btn btn-danger btn-width-xxs" @click="close()">Close Ticket</button>
                                    <button class="btn btn-info btn-width-xs btn-xs-block">Close Ticket</button>
                                </confirm>
                                <div v-else>
                                    <button class="btn btn-info btn-width-xs btn-xs-block" @click="reopen()">Reopen Ticket</button>
                                </div>
                            </div>

                            <ticket-chat :ticket="ticket" show-for="vendor" @send="send" class="mt-6"/>

                        </div>
                        <div class="col-sm-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Confirm from '@/common/components/Confirm';
import TicketChat from '@/common/components/TicketChat';

export default {
    name: 'TicketPage',
    inject: ['$ticketService'],
    components: {
        Confirm,
        TicketChat},
    data() {
        return {
            loading: true,
            showNotFound: false,
            ticket: null
        };
    },
    created() {
        this.$ticketService.getTicketById(this.$route.params.key)
            .then(ticket => {
                this.ticket = ticket;
                this.loading = false;
            })
            .catch(() => {
                this.loading = false;
                this.showNotFound = true;
            });
    },
    methods: {
        send(text) {
            return this.$ticketService.sendMessageToTicket(this.ticket.key, {text})
                .then(ticket => {
                    this.ticket = ticket;
                });
        },
        close() {
            this.$ticketService.updateTicket(this.ticket.key, {status: 'closed'})
                .then(ticket => {
                    this.ticket = ticket;
                });
        },
        reopen() {
            this.$ticketService.updateTicket(this.ticket.key, {status: 'opened'})
                .then(ticket => {
                    this.ticket = ticket;
                    this.$flashSuccess('Ticket has been reopened.');
                });
        }
    }
};
</script>
