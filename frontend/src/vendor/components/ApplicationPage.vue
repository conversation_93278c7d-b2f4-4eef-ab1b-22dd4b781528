<template>
    <div v-qa="'application-block'">
        <!-- create -->
        <div v-if="application.status === 'create' || application.status === 'Draft'">
            <div>
                <h2 v-if="application.status === 'create'" class="text-300 mt-0 mb-6 text-size-32">
                    Activate Account
                </h2>
                <h2 v-else-if="application.status === 'Draft'" class="text-300 mt-0 mb-6 text-size-32">
                    Finish Application
                </h2>
                <h2 v-else class="text-300 mt-0 mb-6 text-size-32">
                    Submit Application
                </h2>

                <div class="panel panel-default mb-0">
                    <div class="p-3 p-sm-6">
                        <application-form :application.sync="applicationForm" :states="states" :stateP="state"
                            :status="application.status" @send="send" @finish="finish" />
                    </div>
                </div>
            </div>
        </div>

        <!-- pending -->
        <div v-else-if="application.status === 'Pending'" v-qa="'pending-status'">
            <div v-if="!showEditForm" class="pt-7 pb-6 px-6 text-black bg-white" style="box-shadow: 0 5px #1aa79e inset">
                <div class="d-flex mb-2">
                    <div class="mr-3">
                        <div class="icon-slot" style="width: 30px;">
                            <span class="p-abs centered text-brand-info text-size-30">
                                <img svg-inline class="icon d-block" src="@images/inline-svg/icons/info-outline.svg" alt="">
                            </span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <h5 class="my-0">
                            Your account activation is pending
                        </h5>
                    </div>
                </div>
                <div class="d-flex">
                    <div class="mr-3 text-size-30" style="height: 1px">
                        <div class="icon"></div>
                    </div>
                    <div>
                        <div class="mb-1">
                            You have submitted your vendor application on {{ application.submittedAt | datetime }}. Feel free to add your products now! Until
                            your
                            application is accepted, none of your products will appear on canideal.com. The review process
                            normally takes 1 to 5 business days.
                        </div>
                        <div class="mt-2" v-if="this.applicationForm.adminNotes">
                            <div class="alert alert-warning" role="alert">
                                <strong>Notes : </strong>
                                {{ this.applicationForm.adminNotes }}
                            </div>
                        </div>
                        <div class="mt-3">
                            <button @click="showEditForm = true" class="btn btn-outline-primary btn-width-lg btn-xs-block">
                                <b>Edit Application</b>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit form for pending applications -->
            <div v-if="showEditForm">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="text-300 mt-0 mb-0 text-size-32">Edit Application</h2>
                    <button @click="showEditForm = false" class="btn btn-outline-secondary">
                        Cancel
                    </button>
                </div>
                <div class="panel panel-default mb-0">
                    <div class="p-3 p-sm-6">
                        <application-form :application.sync="applicationForm" :states="states" :stateP="state"
                            :status="'edit'" @send="send" @finish="finish" />
                    </div>
                </div>
            </div>
        </div>

        <!-- declined -->
        <div v-else-if="application.status === 'Declined'" v-qa="'declined-status'">
            <div class="pt-7 pb-6 px-6 text-black bg-white" style="box-shadow: 0 5px #EB5757 inset">
                <div class="d-flex mb-2">
                    <div class="mr-3">
                        <div class="icon-slot" style="width: 30px;">
                            <span class="p-abs centered text-brand-danger text-size-30">
                                <img svg-inline class="icon d-block" src="@images/inline-svg/icons/cancel.svg" alt="">
                            </span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <h5 class="my-0">
                            Your vendor application has been declined
                        </h5>
                    </div>
                </div>
                <div class="d-flex">
                    <div class="mr-3 text-size-30" style="height: 1px">
                        <div class="icon"></div>
                    </div>
                    <div>
                        <div class="mb-1">
                            We processed your vendor application on {{ application.declinedAt | datetime }}.
                            Unfortunately, your CanIDeal account can't be activated at this time because of incomplete or
                            invalid information.
                        </div>
                        <div class="mt-2" v-if="this.applicationForm.adminNotes">
                            <div class="alert alert-danger" role="alert">
                                <strong>Notes : </strong>
                                {{ this.applicationForm.adminNotes }}
                            </div>
                        </div>
                        <div class="mt-3">
                            <button @click="createApplication()" class="btn btn-primary btn-width-lg btn-xs-block">
                                <b>Resubmit Vendor Application</b>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--expired-->
        <div v-else-if="application.status === 'Expired'" v-qa="'expired-status'">
            <div class="pt-7 pb-6 px-6 text-black bg-white" style="box-shadow: 0 5px #EB5757 inset">
                <div class="d-flex mb-2">
                    <div class="mr-3">
                        <div class="icon-slot" style="width: 30px;">
                            <span class="p-abs centered text-brand-danger text-size-30">
                                <img svg-inline class="icon d-block" src="@images/inline-svg/icons/cancel.svg" alt="">
                            </span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <h5 class="my-0">
                            Your vendor application has been expired

                        </h5>
                    </div>
                </div>
                <div class="d-flex">
                    <div class="mr-3 text-size-30" style="height: 1px">
                        <div class="icon"></div>
                    </div>
                    <div>
                        <div class="mb-1">

                            {{ docExpired ? 'One or more of your required documents has expired. ' : `Your application was
                            accepted on ${application.acceptedAt | date} and has expired on
                                                        ${application.expiredAt | date}.` }}
                            Please resubmit the application to continue to use CanIDeal.

                            <!-- Your application was accepted on {{ application.acceptedAt | date }} and has been expired on
                            {{ application.expiredAt | date }}.
                            Please resubmit the application to continue to use CanIDeal -->
                        </div>
                        <div class="mt-3">
                            <button @click="createApplication()" class="btn btn-primary btn-width-lg btn-xs-block">
                                <b>Resubmit Vendor Application</b>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- accepted -->
        <div v-else-if="application.status === 'Accepted'" v-qa="'accept-status'">
            <div v-if="!showEditForm" class="pt-7 pb-6 px-6 text-black bg-white" style="box-shadow: 0 5px #8dc640 inset">
                <div class="d-flex mb-6">
                    <div class="mr-3">
                        <div class="icon-slot" style="width: 30px;">
                            <span class="p-abs centered text-brand-success text-size-30">
                                <img svg-inline class="icon d-block" src="@images/inline-svg/icons/check-circle.svg" alt="">
                            </span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <h5 class="my-0">
                            Your store is now OPEN. Good Luck!
                        </h5>
                    </div>
                </div>
                <div class="d-flex flex-wrap">
                    <div class="mr-3 text-size-30" style="height: 1px">
                        <div class="icon"></div>
                    </div>
                    <div class="d-flex flex-wrap gap-2">
                        <router-link to="/products/new" class="btn btn-primary btn-width-lg btn-xs-block mr-2">
                            <b>+ Add Product</b>
                        </router-link>
                        <button @click="showEditForm = true" class="btn btn-outline-primary btn-width-lg btn-xs-block">
                            <b>Edit Application</b>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Edit form for accepted applications -->
            <div v-if="showEditForm">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="text-300 mt-0 mb-0 text-size-32">Edit Application</h2>
                    <button @click="showEditForm = false" class="btn btn-outline-secondary">
                        Cancel
                    </button>
                </div>
                <div class="panel panel-default mb-0">
                    <div class="p-3 p-sm-6">
                        <application-form :application.sync="applicationForm" :states="states" :stateP="state"
                            :status="'edit'" @send="send" @finish="finish" />
                    </div>
                </div>
            </div>
        </div>

        <!-- no application -->
        <div v-else v-qa="'no-application-status'">
            <div class="pt-7 pb-6 px-6 text-black bg-white" style="box-shadow: 0 5px #ff7a00 inset">
                <div class="d-flex mb-2">
                    <div class="mr-3">
                        <div class="icon-slot" style="width: 30px;">
                            <span class="p-abs centered text-brand-warning text-size-30">
                                <img svg-inline class="icon d-block" src="@images/inline-svg/icons/warning.svg" alt="">
                            </span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <h5 class="my-0">
                            Your account requires activation
                        </h5>
                    </div>
                </div>
                <div class="d-flex">
                    <div class="mr-3 text-size-30" style="height: 1px">
                        <div class="icon"></div>
                    </div>
                    <div>
                        <div class="mb-1">
                            Until your vendor account is approved, none of your products will appear on canideal.com. The
                            review process normally takes 3 to 5
                            business days. To activate your account please fill and submit application below.
                        </div>
                        <div class="mt-3">
                            <button @click="createApplication()" class="btn btn-primary btn-width-lg btn-xs-block"
                                v-qa="'application-btn'">
                                <b>Activate Account</b>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApplicationForm from '@/vendor/components/ApplicationForm';
import { mapActions, mapState } from 'vuex';

export default {
    name: 'ApplicationPage',
    inject: ['$vendorService'],
    components: {
        ApplicationForm
    },
    data() {
        return {
            applicationForm: {
                businessName: '',
                tradeName: '',
                ein: '',
                legalAddress: {
                    line1: '',
                    line2: '',
                    city: '',
                    postal: '',
                    region: this.$store.getters.state.id
                },
                fullName: '',
                phone: '',
                description: '',
                monthlySales: '',
                license: {
                    cannabis: false,
                    number: '',
                    name: '',
                    licenseType: '',
                    businessName: '',
                    county: ''
                },
                files: [],
                proofOfAddress: undefined,
                personalId: undefined,
                agreeTerms: false,
                adminNotes: ""
            },
            state: {},
            docExpired: true,
            showEditForm: false
        };
    },
    created() {
        this.state = this.$store.getters.state;

        this.$vendorService.getApplication().then(({ ok, application }) => {
            if (ok && application.status === "Draft") {
                this.applicationForm = application;
            } else {
                this.applicationForm.businessName = this.application.businessName ? this.application.businessName : this.vendor.storeName || '';
                this.applicationForm.license.businessName = this.application.businessName ? this.application.businessName : this.vendor.storeName || '';
                this.applicationForm.tradeName = this.application.tradeName || '';
                this.applicationForm.ein = this.application.ein || '';
                if (this.application.legalAddress) {
                    this.applicationForm.legalAddress = { ...this.application.legalAddress };
                }
                this.applicationForm.fullName = this.application.fullName || '';
                this.applicationForm.phone = this.application.phone || '';
                if (this.application.license && this.application.license.cannabis) {
                    this.applicationForm.license.cannabis = this.application.license.cannabis;
                }
                if (this.application.files && this.application.files.length > 0) {
                    this.applicationForm.files = this.application.files;
                }
                else {
                    this.applicationForm.files = [];
                }

                this.applicationForm.adminNotes = this.application.adminNotes && this.application.adminNotes.trim();
            }
        });


    },
    computed: {
        ...mapState(['states', 'vendor']),
        application() {
            return this.vendor.application || {};
        },
        saveDisabled() {
            return !this.agreeTerms;
        },
        requiredDocuments() {
            return this.state.requiredDocuments || [];
        },
        requiredDocIds() {
            return this.requiredDocuments.map(doc => doc._id);
        }
    },
    methods: {
        ...mapActions(['createApplication', 'sendApplication', 'finishApplication']),
        send(isDraft) {
            // If application already exists (has an ID), update it instead of creating new
            const hasExistingApplication = this.application && (this.application._id || this.application.id);

            let promise;
            if (hasExistingApplication) {
                // Update existing application
                promise = this.finishApplication({
                    ...this.applicationForm,
                    agreeTermsOfServiceAndPrivacyPolicy: true,
                    agreeUserFeesAndPaymentPolicy: true,
                    agreeReturnPolicy: true,
                    forDraft: isDraft
                });
            } else {
                // Create new application
                promise = this.sendApplication({
                    ...this.applicationForm,
                    agreeTermsOfServiceAndPrivacyPolicy: true,
                    agreeUserFeesAndPaymentPolicy: true,
                    agreeReturnPolicy: true,
                    isDraft
                });
            }

            if (isDraft) {
                this.$emit('isSaving', promise);
            } else {
                this.$emit('loading', promise);
            }

            if (!isDraft) {
                promise
                    .then(() => {
                        this.$router.push('/');
                    })
                    .catch(err => {
                        this.$addErrors(err);
                    });
            }
            return;
        },
        finish(forDraft) {
            // Always use finishApplication for the finish method (updates existing application)
            const promise = this.finishApplication({
                ...this.applicationForm,
                agreeTermsOfServiceAndPrivacyPolicy: true,
                agreeUserFeesAndPaymentPolicy: true,
                agreeReturnPolicy: true,
                forDraft
            });
            this.$emit('loading', promise);
            promise
                .then(() => {
                    this.$router.push('/');
                })
                .catch(err => {
                    this.$addErrors(err);
                });
        },
        checkDocExpiration() {
            if (this.application && this.application.files.length > 0) {
                for (let i = 0; i < this.application.files.length; i++) {
                    const file = this.application.files[i];
                    const docIndex = this.requiredDocIds.indexOf(file.docName);
                    if (docIndex !== -1 && this.requiredDocuments[docIndex].expirationRequired) {
                        const expirationDate = new Date(this.requiredDocuments[docIndex].expirationDate);
                        if (expirationDate < new Date()) {
                            this.docExpired = true; // set docExpired to true if an expired document is found
                            break; // exit loop if an expired document is found
                        }
                    }
                }

            }

        }
    }
};
</script>
