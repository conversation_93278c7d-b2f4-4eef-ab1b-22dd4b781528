<template>
    <div v-if="state === 'success'">
        <div class="modal-header">
            <div class="d-flex align-items-center">
                <div>
                    <h4 class="my-3 text-500 text-size-20">
                        Ticket Created
                    </h4>
                </div>
                <div class="ml-auto">
                    <span class="cursor-pointer text-gray text-h4" @click="hide()" aria-label="Close">
                        <svg class="icon d-block" viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12Z"
                                  fill-rule="evenOdd"></path>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
        <div class="modal-body">
            <div class="text-center mb-2">
                <img svg-inline class="icon icon-3x" src="@images/inline-svg/success.svg" alt="">
            </div>
            <div class="text-center">
                Your support ticket has been created successfully.
            </div>
            <div class="text-center">
                Our support team will review your ticket and respond within 1-2 business days.
            </div>
        </div>
        <div class="modal-footer">
            <div class="text-center">
                <button class="btn btn-info btn-xs-block btn-width-md" @click="hide()">
                    OK
                </button>
            </div>
        </div>
    </div>
    <div v-else>
        <div class="modal-header">
            <div class="d-flex align-items-center">
                <div>
                    <h4 class="my-3 text-500 text-size-20">
                        Create Support Ticket
                    </h4>
                </div>
                <div class="ml-auto">
                    <span class="cursor-pointer text-gray text-h4" @click="hide()" aria-label="Close">
                        <svg class="icon d-block" viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12Z"
                                  fill-rule="evenOdd"></path>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
        <form @submit.prevent="send()">
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-sm-3">
                            Category <span class="text-brand-danger">*</span>
                        </label>
                        <div class="col-sm-8" :class="{'has-error': errors.has('category')}">
                            <div class="form-control-select">
                                <select name="category" class="form-control" v-model="category" v-validate="'required'">
                                    <option value="">Select a category</option>
                                    <option value="account_issues">Account Issues</option>
                                    <option value="login_problems">Login Problems</option>
                                    <option value="product_management">Product Management</option>
                                    <option value="payment_issues">Payment Issues</option>
                                    <option value="general_support">General Support</option>
                                </select>
                                <span class="form-control-select-arrow">
                                    <span class="caret"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-3">
                            Subject <span class="text-brand-danger">*</span>
                        </label>
                        <div class="col-sm-8" :class="{'has-error': errors.has('subject')}">
                            <input type="text" class="form-control" v-model="subject" name="subject"
                                   v-validate="'required'" placeholder="Brief description of your issue">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-3">
                            Message <span class="text-brand-danger">*</span>
                        </label>
                        <div class="col-sm-8" :class="{'has-error': errors.has('text')}">
                            <textarea class="form-control" cols="30" rows="5" v-model="message" name="text"
                                      v-validate="'required'" placeholder="Please provide detailed information about your issue"></textarea>
                        </div>
                    </div>
                    <div class="form-group mb-0">
                        <label class="control-label col-sm-3">
                            Images
                        </label>
                        <div class="col-sm-6">
                            <div class="m-1" v-for="image in images" :key="image.uuid">
                                <div class="p-rel hover-visible cursor-pointer">

                                    <div class="img-upload-th border-gray-5 border-radius-3 bg-contain"
                                         :style="`background-image: url(https://ucarecdn.com/${image.uuid}/-/resize/300/)`">
                                        <img :src="`https://ucarecdn.com/${image.uuid}/-/resize/300/`"
                                             class="img-upload-th invisible" alt="">
                                    </div>

                                    <div class="p-abs top-right p-1 hover-visible-child">
                                        <span class="cursor-pointer text-white bg-danger border-radius-circle p-1"
                                              @click="removeImage(image)">
                                            <svg class="icon icon-xs" viewBox="0 0 24 24">
                                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12Z"
                                                      fill-rule="evenodd"></path>
                                            </svg>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <uploader accept="image/*" :multiple="true" :crop="false"
                                          @file="addImage"></uploader>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-center">
                    <div>
                        <button class="btn btn-info btn-xs-block btn-width-md" :disabled="errors.any()">
                            Create Ticket
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</template>

<script>
import ModalMixin from '@/common/plugins/modal/ModalMixin';

export default {
    name: 'CreateTicketModal',
    mixins: [ModalMixin],
    inject: ['$ticketService'],
    data() {
        return {
            category: '',
            subject: '',
            message: '',
            images: [],
            state: 'createTicket'
        };
    },
    methods: {
        async send() {
            const result = await this.$validator.validateAll();
            if (!result) {
                return Promise.reject(result);
            }

            this.$ticketService.createTicket({
                category: this.category,
                subject: this.subject,
                message: this.message,
                images: this.images
            })
                .then(() => {
                    this.state = 'success';
                    this.$emit('success');
                })
                .catch(err => {
                    this.$flashError(err);
                });
        },
        addImage(files) {
            if (!Array.isArray(files)) {
                files = [files];
            }
            this.images = this.images.concat(files);
        },
        removeImage(file) {
            this.images = this.images.filter(image => image.uuid !== file.uuid);
        }
    }
};
</script>
