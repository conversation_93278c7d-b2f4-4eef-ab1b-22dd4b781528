<template>
    <smart-table :data="ticketList" @update="queryOrderList" spinner-background-class="bg-body">
        <div class="d-flex d-xs-block align-items-center mb-6" slot="caption">
            <div>
                <h2 class="text-300 my-0 text-size-32" v-qa="'ticketList-page'">
                    Tickets
                </h2>
            </div>
            <div class="mr-3 hidden-xs"></div>
            <div class="mt-3 visible-xs-block"></div>
            <div class="ml-auto">
                <div class="d-flex d-xs-block align-items-center">
                    <div class="mr-3">
                        <button class="btn btn-info d-xs-block" @click="openCreateTicketModal" v-qa="'newTicket-btn'">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill-rule="evenodd"></path>
                            </svg>
                            Create New Ticket
                        </button>
                    </div>
                    <div>
                        <div class="form-control-select">
                            <select name="state" class="form-control" v-st-filter="'status'">
                                <option value="">All Statuses</option>
                                <option value="opened">Open</option>
                                <option value="closed">Closed</option>
                            </select>
                            <span class="form-control-select-arrow">
                                <span class="caret"></span>
                            </span>
                        </div>
                    </div>
                    <div class="mr-3 hidden-xs"></div>
                    <div class="mt-2 visible-xs-block"></div>
                    <div>
                        <div class="form-group has-feedback mb-0">
                            <input type="text" class="form-control" placeholder="Search Ticket"
                                   v-st-filter:input="'q'">
                            <span class="form-control-feedback" aria-hidden="true">
                                <svg class="icon text-brand-primary" viewBox="0 0 24 24">
                                    <path d="M15.5 14H14.71L14.43 13.73A6.47 6.47 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49 20.49 19 15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14Z"
                                          fill-rule="evenodd"></path>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template slot="header">
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="key">
                    <div class="mr-1">
                        Ticket ID
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="retailer.businessName">
                    <div class="mr-1">
                        Type
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="subject">
                    <div class="mr-1">
                        Subject
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="status">
                    <div class="mr-1">
                        Status
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="createdAt">
                    <div class="mr-1">
                        Created At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
            <th class="cursor-pointer text-gray-dark">
                <st-sort class="d-flex" name="updatedAt">
                    <div class="mr-1">
                        Updated At
                    </div>
                    <st-sort-icons/>
                </st-sort>
            </th>
        </template>

        <template slot="row" slot-scope="{item}">
            <router-link :to="`/tickets/${item.key}`" tag="tr">
                <td>
                    {{item.key}}
                </td>
                <td>
                    <div class="text-md-wrap">
                        <template v-if="item.retailer">
                            {{item.retailer.businessName}}
                        </template>
                        <template v-else>
                            <span class="text-info">Support</span>
                        </template>
                    </div>
                </td>
                <td>
                    <div class="text-md-wrap">
                        {{item.subject || '–'}}
                    </div>
                </td>
                <td class="text-nowrap"
                    :class="{'text-brand-warning': item.status === 'opened', 'opacity-60': item.status === 'closed'}">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M17.5 12c0 3.033-2.467 5.5-5.5 5.5A5.506 5.506 0 0 1 6.5 12c0-3.033 2.467-5.5 5.5-5.5s5.5 2.467 5.5 5.5z"
                              fill-rule="evenodd"></path>
                    </svg>
                    <template v-if="item.status === 'opened'">
                        Open
                    </template>
                    <template v-else-if="item.status === 'closed'">
                        Closed
                    </template>
                    <template v-else>
                        {{item.status}}
                    </template>
                </td>
                <td>
                    <div class="text-nowrap">{{item.createdAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.createdAt | time}}
                    </div>
                </td>
                <td>
                    <div class="text-nowrap">{{item.updatedAt | date}}</div>
                    <div class="text-size-13 opacity-60">
                        {{item.updatedAt | time}}
                    </div>
                </td>
            </router-link>
        </template>

        <template slot="emptyPromo">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg viewBox="0 0 24 24" class="icon icon-5x opacity-50">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                No tickets found
            </div>
        </template>
        <template slot="empty">
            <div class="pt-9 pb-12 px-3 text-center">
                <div class="mb-2">
                    <svg viewBox="0 0 24 24" class="icon icon-5x opacity-50">
                        <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z"
                              fill-rule="evenodd"></path>
                    </svg>
                </div>
                No tickets found
            </div>
        </template>
    </smart-table>
</template>

<script>
import {mapState} from 'vuex';
import CreateTicketModal from './CreateTicketModal';

export default {
    name: 'TicketListPage',
    inject: ['$ticketService'],
    data() {
        return {
            ticketList: []
        };
    },
    computed: {
        ...mapState(['vendor'])
    },
    methods: {
        queryOrderList({filters, pagination}) {
            const promise = this.$ticketService.queryTicketList({...filters, ...pagination})
                .then(ticketList => {
                    this.ticketList = ticketList;
                });
            this.$root.$emit('loading', promise);
            promise
                .catch(err => {
                    this.$flashError(err);
                });
        },
        openCreateTicketModal() {
            this.$modal.open(CreateTicketModal, {
                size: 'modal-800',
                on: {
                    success: () => {
                        // Refresh the ticket list after successful creation
                        this.queryOrderList({filters: {}, pagination: {}});
                    }
                }
            });
        }
    }
};
</script>
