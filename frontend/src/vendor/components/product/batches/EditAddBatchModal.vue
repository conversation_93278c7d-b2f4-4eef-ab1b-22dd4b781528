<template>
    <div>
        <div class="modal-header">
            <div class="d-flex align-items-center">
                <div>
                    <h4 class="my-3 text-500 text-size-20">
                        Batch
                    </h4>
                </div>
                <div class="ml-auto">
                    <span class="cursor-pointer text-gray text-h4" aria-label="Close" @click="hide">
                        <svg class="icon d-block" viewBox="0 0 24 24">
                            <path
                                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12Z"
                                fill-rule="evenOdd"></path>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
        <div class="modal-body">
            <div class="form-horizontal">
                <div class="form-group mb-3">
                    <label class="control-label col-sm-3 text-400">
                        <span class="d-block">
                            Name
                        </span>
                    </label>
                    <div class="col-sm-6">
                        <div class="d-flex">
                            <div class="input-group">
                                <input class="form-control" type="text" name="Name" v-model="value.name">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3" :class="{ 'has-error': errors.has('batchesID') }">
                    <label class="control-label col-sm-3 text-400">
                        <span class="d-block">
                            Batch ID <span class="text-brand-danger">*</span>
                        </span>
                    </label>
                    <div class="col-sm-6">
                        <input-error class="form-control" name="batchesID" :value="value.batchID"
                            @input="updateBatchID" v-validate="'required|batchesID'" />
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="control-label col-sm-3 text-400">
                        <span class="d-block">
                            Certificate of Analysis (COA) <span class="text-brand-danger">*</span>
                        </span>
                    </label>
                    <div class="col-sm-6">
                        <div class="d-flex">
                            <div>
                                <div :class="{ 'm-1': value.coa.length }" v-for="data in value.coa" :key="data.uuid">
                                    <pdf-preview v-if="data.name.includes('.pdf')" :pdf="data" @removePdf="removeBatchImage(data)" can-remove />
                                    <image-preview v-else :image="data" @removeImage="removeBatchImage(data)" can-remove />
                                </div>
                                <div class="m-1">
                                    <uploader :custom-button="true"
                                        :imagesOnly = "false"
                                        :multiple = "false"
                                        @file="addBatchImage">
                                    </uploader>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="d-flex align-items-center justify-content-space-between m-minus-1">
                <div class="m-1">
                    <button type="button" class="btn btn-default btn-xs-block btn-width-xs" @click="hide()">
                        Cancel
                    </button>
                </div>
                <div class="m-1">
                    <button v-if="value && (Object.keys(value).length > 1)" type="button"
                        class="btn btn-info btn-xs-block btn-width-xs" @click="checkAndClose">
                        Apply
                    </button>
                    <button v-else type="button" class="btn btn-info btn-xs-block btn-width-xs" @click="checkAndClose">
                        Add Batch
                    </button>

                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ImagePreview from '@/common/components/ImagePreview';
import ModalMixin from '@/common/plugins/modal/ModalMixin';
import batchMixin from '@/common/plugins/Mixins/batchMixin';
import PdfPreview from '@/common/components/PdfPreview';

export default {
    name: 'EditAddBatchModal',
    components: {
        ImagePreview,
        PdfPreview
    },
    mixins: [ModalMixin, batchMixin],
    props: {
        existErrors: Array,
        batches: Array,
        batch: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            value: { variants: [], ...this.batch }
        };
    },
    created() {
        // Validate if the batch ID is already used.
        this.$validator.extend('batchesID', value => {
            if (!value) {
                return false;
            }
            // Count the occurrences of the provided batchID among the batches
            const occurrences = this.batches.reduce((count, batch) => count + (batch.batchID === value ? 1 : 0), 0);
            
            // Return false if the batchID occurs more than once. Otherwise, return true.
            return occurrences < 1 || (this.batch.batchID === value && occurrences === 1);
        });
    },
    mounted() {
        (this.existErrors || []).forEach(({ field, msg }) => {
            if (field in this.fields) {
                this.errors.add({ field, msg });
            }
        });        
    },
    methods: {
        async checkAndClose() {
            const result = await this.$validator.validateAll();

            // Check if COA is present - now optional with warnings
            if (!this.value.coa || this.value.coa.length === 0) {
                // Check if vendor is a broker - brokers don't need COAs
                if (this.$store.state.vendor && this.$store.state.vendor.isBroker) {
                    // Brokers don't need COAs, proceed without warning
                } else {
                    // Show warning for non-broker vendors but allow them to proceed
                    this.$flashWarning(`Warning: No Certificate of Analysis (COA) provided. Your products may be turned off if COA is not uploaded later.`);
                }
            }

            if (!result) {
                this.$flashError(`Please provide a Unique batch ID`);
                return;
            }

            // If the conditions in shouldUpdateBatch are met, update the batch
            if (this.shouldUpdateBatch(this.value, this.batch, this.batches)) {
                this.$emit('success', this.value);

                /**
                * TODO: Call endpoint to create the Batch
                *
                */

                this.hide();
                return;
            } else {
                this.$flashError(`Please provide a Unique batch ID`);
            }

        },
        updateBatchID(event) {
            // Remove any spaces for the batch ID
            const value = event.target.value;
            const transformedValue = value.replace(/\s/g, '_').replace(/[^\d\w]/g, '');
            this.value.batchID = transformedValue;
        },
        uploadBatchMainImage(image) {
            this.value.mainImage = image;
        },
        removeBatchMainImage() {
            this.value.mainImage = {};
        },
        addBatchImage(files) {
            if (!Array.isArray(files)) {
                files = [files];
            }

            if (Array.isArray(this.value.coa)) {
                this.value.coa.push(...files);  // Use spread syntax to push multiple files
                this.$set(this.value, 'coa', [...this.value.coa]);  // Use $set for reactivity
            } else {
                this.$set(this.value, 'coa', files);
            }
        },
        removeBatchImage(file) {
            this.value.coa = this.value.coa.filter(image => image.uuid !== file.uuid);
        }
    }
};
</script>
