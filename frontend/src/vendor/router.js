import Vue from 'vue';
import Router from 'vue-router';
import ApplicationAddDataPage from './components/ApplicationAddDataPage';
import ApplicationPage from './components/ApplicationPage';
import CouponListPage from './components/CouponListPage'
import HomePage from './components/HomePage';
import OrderListPage from './components/OrderListPage';
import OrderPage from './components/OrderPage';
import ProductListPage from './components/ProductListPage';
import ProductPage from './components/ProductPage';
import UserSettings from './components/settings/UserSettings';
import TeamSettings from './components/settings/TeamSettings';
import AddressesSettings from './components/settings/AddressesSettings';
import PaymentSettings from './components/settings/PaymentSettings';
import ReturnPoliciesSettings from './components/settings/ReturnPoliciesSettings';
import VendorSettings from './components/settings/VendorSettings';
import TicketListPage from './components/TicketListPage';
import TicketPage from './components/TicketPage';


Vue.use(Router);


export default function createRouter(store) {
    const router = new Router({
        mode: 'history',
        base: '/vendor',
        linkActiveClass: 'active',
        routes: [
            { path: '/', redirect: '/home', name: 'home' },
            { path: '/home', component: HomePage },
            { path: '/application', component: ApplicationPage },
            { path: '/coupons', component: CouponListPage },
            { path: '/application/add-data', component: ApplicationAddDataPage },
            { path: '/products', component: ProductListPage },
            { path: '/products/:key', name: 'product-page', component: ProductPage },
            { path: '/orders', component: OrderListPage },
            { path: '/orders/:key', component: OrderPage },
            { path: '/tickets', component: TicketListPage },
            { path: '/tickets/:key', component: TicketPage },
            { path: '/settings', component: UserSettings },
            { path: '/settings/team', component: TeamSettings },
            { path: '/settings/store', component: VendorSettings },
            { path: '/settings/payments', component: PaymentSettings },
            { path: '/settings/addresses', component: AddressesSettings },
            { path: '/settings/return-policies', component: ReturnPoliciesSettings }

        ]
    });

    router.beforeEach((to, from, next) => {
        const appStatus = store.getters.application.status;

        // Allow navigation to /settings/* routes regardless of application status
        if (to.path.startsWith('/settings')) {
            return next();
        }

        // Redirect to /application if the application status is 'Draft', and the target is not /application
        if (appStatus === 'Draft' && to.path !== '/application') {
            return next('/application');
        }

        // Redirect from /application to /home if the status is 'Accepted' or 'Pending'
        if (['Accepted', 'Pending'].includes(appStatus) && to.path === '/application') {
            return next('/home');
        }

        // Allow access to /home and other pages for 'Accepted' and 'Pending' statuses
        // If trying to access /home and the status requires application completion, redirect to /application
        if (!['Accepted', 'Pending'].includes(appStatus) && to.path === '/home') {
            return next('/application');
        }

        next();
    });

    return router;
}
