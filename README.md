# CanIDeal

New marketplace!


# Developing (local environment)

## Requirements

* node 10 (LTS)
* npm latest
* mongodb 4.2 / or docker


## Prerequisite!
```
nvm use 10
```

## Install

```
npm install
```

## Run

If you don't have mongodb, but have docker:
```bash
docker run --name mongo -p27017:27017 -d mongo:4.2
```

Run migrations:
```
npm run migrate
```

Next, start app:
```
npm run start
```
Next, queue app:
```
npm run start:queue
```
And webpack
```
npm run watch
```

* http://localhost:9000 - CanIDeal served with webpack
* http://localhost:3000 - CanIDeal node js app without styles/images/another static content


## Test

Run all test:
```
npm run test:report
```

# Production

### Local Dependencies
* docker
* docker-registry
* make
* python-pip
* ansible via pip or will receive error

## Remove cached and dangling builds
```
docker system prune
```

## Change url and mongo values to production values in config.js

## STAGING SITE: 
### Change url in config.js to demo.canideal.com
### Change GIT_HASH?=production to GIT_HASH?=staging in Makefile
### continue below code

## Build docker images
```
make docker
```

## Run test 
```shell script
make production_tests
```

## Build docker images and upload to docker registry
```
make upload
```


## Deploy production server use docker registry and ansible playbook 
## Production ONLY
### Must have ansible installed locally 
```shell script
ansible-playbook -v -i devops/ansible/inventory.yml devops/ansible/production.yml
```

### Staging ONLY
```shell script
ansible-playbook -v -i devops/ansible/inventory.yml devops/ansible/staging.yml
```

## Important steps after ansible update (need to modify devops to automate this)
### Make migrations to backend container
```
docker exec -it <containername> /bin/sh -c "npm run migrate"
```
### Update html files Location /usr/share/nginx/html in frontend static container (need to modify code to handle cached files)
``` 
mv oldfiles.234f.js newfiles.js 
```


## Access docker containers
```
docker exec -it <DOCKERID> /bin/sh
```

alpine based so use apk instead of apt-get for installing like vim

```
apk update
```

View Logs
```
docker logs -f <DOCKERNAME>
```

## Restore DB from backup gzip: 
mongorestore --gzip --port=<PORTNUBER> --archive=recent_backup.gzip --nsFrom 'canideal.*' --nsTo 'canideal.*'
i.e.
https://davejansen.com/how-to-dump-restore-a-mongodb-database-from-a-docker-container/

## Update local mongo db from remote server:
ssh -i /Users/<USER>/.ssh/id_rsa_canideal root@<production server IP> \
  "docker exec <containerID> /usr/bin/mongodump --db canideal-production --archive --gzip" \
| docker exec -i mongo mongorestore --archive --gzip --drop

## server to server
scp -r root@<IP_ADDRESS>:/root/ /

## Create or renew letsencrypt certificates
./renew_certificates.sh or ./request_certificates.sh in base file system for server

## Create a local mongobd GUI on http:/localhost:8081
```bash
docker run --name mongo-express \
  --link mongo:mongo -p 8081:8081 \
  -e ME_CONFIG_BASICAUTH_USERNAME="admin" \
  -e ME_CONFIG_BASICAUTH_PASSWORD="secret" \
  -e ME_CONFIG_MONGODB_ENABLE_ADMIN="true" \
  -e ME_CONFIG_MONGODB_URL="mongodb://mongo:27017" \
  -d mongo-express
```

## Run migrations manually
```bash
docker exec -it <backend-container-id> /bin/sh
```

```bash
npm run migrate
```

